-- @Author: 小九呀丶 - QQ：5268416
-- @Date:   2024-05-12 22:27:45
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-07-08 20:51:41
local 场景类_战斗自动栏 = class()
local 控制状态 = "关闭"
local 开启自动 = false
local format = string.format
local tp, zts
local 配置文件路径 = "./config.ini"

-- 添加UI切换状态
local UI切换状态 = "完整" -- "完整" 或 "简化"

-- 初始化自动战斗栏位置（默认值）
local 自动战斗栏位置 = {
    x = tonumber(读配置(配置文件路径, "mhxy", "自动战斗栏位置X")) or 20,
    y = tonumber(读配置(配置文件路径, "mhxy", "自动战斗栏位置Y")) or 300
}

if not 自动战斗栏位置 then
    print("加载位置失败：配置文件中未找到位置，使用默认位置")
end

function 场景类_战斗自动栏:初始化(根)
	self.ID = 903
	self.x = 自动战斗栏位置.x -- 使用保存的 x 坐标
    self.y = 自动战斗栏位置.y -- 使用保存的 y 坐标

	self.xx = 0
	self.yy = 0
	self.注释 = "战斗"
	self.可视 = false
	self.可视化 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	self.窗口时间 = 0
	self.状态 = "取消" -- 将状态变量改为对象属性


	local 资源 = 根.资源
	local 按钮 = 根._按钮
	local 自适应 = 根._自适应
	self.资源组 = {
		[0] = 自适应.创建(1, 1, 255 - 25, 18, 1, 3, nil, 18),
		[1] = 自适应.创建(0, 1, 255, 180, 3, 9),
		[2] = 按钮.创建(自适应.创建(18, 4, 16, 16, 4, 3), 0, 0, 4, true, true),--关闭
		[3] = 按钮.创建(资源:载入('wzife.wdf', "网易WDF动画", 0x49d09c8b), 0, 0, 4, true, true),--切换按钮
		[4] = 资源:载入('wzife.wdf', "网易WDF动画", 0x6d24a02d),--简化模式背景
		[13] = 按钮.创建(自适应.创建(12, 4, 60, 22, 1, 3), 0, 0, 4, true, true, " 自动"),
		[14] = 按钮.创建(自适应.创建(12, 4, 60, 22, 1, 3), 0, 0, 4, true, true, " 攻击"),
		[15] = 按钮.创建(自适应.创建(12, 4, 60, 22, 1, 3), 0, 0, 4, true, true, " 防御"),
		[16] = 按钮.创建(自适应.创建(12, 4, 60, 22, 1, 3), 0, 0, 4, true, true, " 自动"),
		[17] = 按钮.创建(自适应.创建(12, 4, 60, 22, 1, 3), 0, 0, 4, true, true, " 攻击"),
		[18] = 按钮.创建(自适应.创建(12, 4, 60, 22, 1, 3), 0, 0, 4, true, true, " 防御"),
		[19] = 按钮.创建(自适应.创建(12, 4, 60, 22, 1, 3), 0, 0, 4, true, true, " 自动"),
		[20] = 按钮.创建(自适应.创建(12, 4, 60, 22, 1, 3), 0, 0, 4, true, true, " 攻击"),
		[21] = 按钮.创建(自适应.创建(12, 4, 60, 22, 1, 3), 0, 0, 4, true, true, " 防御"),
		[22] = 按钮.创建(自适应.创建(12, 4, 60, 22, 1, 3), 0, 0, 4, true, true, " 自动"),
		[23] = 按钮.创建(自适应.创建(12, 4, 60, 22, 1, 3), 0, 0, 4, true, true, " 攻击"),
		[24] = 按钮.创建(自适应.创建(12, 4, 60, 22, 1, 3), 0, 0, 4, true, true, " 防御"),
		[6] = 资源:载入('wzife.wdf', "网易WDF动画", 0x363AAF1B),
		[7] = 资源:载入('org.rpk', "网易WDF动画", 0x1000368),
		[50] = 资源:载入('wzife.wdf', "网易WDF动画", 0X2E8758EE),
	}
	self.人物技能 = {}
	self.宝宝技能 = {}
	for n = 1, 5 do
		self.人物技能[n] = 根._技能格子(0, 0, i, "辅助技能")
		self.人物技能[n]:置技能()
		self.宝宝技能[n] = 根._技能格子(0, 0, i, "辅助技能")
		self.宝宝技能[n]:置技能()
	end
	for i = 13, 24 do
		self.资源组[i]:绑定窗口_(self.ID)
	end
	tp = 根
	zts = tp.字体表.华康字体1
	self.第一次 = false
	self.加入 = 0
	self.自动数据 = nil
end

function 场景类_战斗自动栏:显示(dt, x, y)
	if not self.自动数据 then
		return
	end
	self.焦点 = false

	-- 检查UI切换状态
	if UI切换状态 == "简化" then
		-- 计算文字宽度并显示简化模式
		local 文字宽度 = tp.字体表.华康字体:取宽度("自动战斗开启中")
		local 背景宽度 = 文字宽度 + 30
		local 背景高度 = 22

		-- 设置背景区域并显示
		self.资源组[4]:置区域(0, 0, 背景宽度, 背景高度)
		self.资源组[4]:显示(self.x, self.y)
		-- 显示切换按钮
		self.资源组[3]:显示(self.x + 2, self.y + 2)
		-- 显示文字
		tp.字体表.华康15粗:置颜色(0xFFFFFFFF):显示(self.x + 30, self.y + 4, "自动战斗中")

		-- 处理切换按钮点击
		self.资源组[3]:更新1(x, y)
		if self.资源组[3]:事件判断() then
			UI切换状态 = "完整"
		end
		return
	end

	-- 完整UI显示（恢复原始版本逻辑）
	self.资源组[1]:显示(self.x, self.y)
	self.资源组[0]:显示(self.x + 3, self.y + 3)
	self.资源组[2]:显示(self.x + self.资源组[1].宽度 - 20, self.y + 3)
	-- 显示切换按钮
	self.资源组[3]:显示(self.x + 1, self.y + 3)
	tp.窗口标题背景_:置区域(0, 0, 80, 16)
	tp.窗口标题背景_:显示(self.x + 255 / 2 - 40, self.y + 3)
	tp.字体表.华康字体:置颜色(0xFFFFFFFF):显示(self.x + self.资源组[1].宽度 / 2 - tp.字体表.华康字体:取宽度("自动战斗") / 2, self.y + 3, "自动战斗")

	-- 处理切换按钮点击
	self.资源组[3]:更新1(x, y)
	if self.资源组[3]:事件判断() then
		UI切换状态 = "简化"
	end
	zts:置颜色(白色)
	if 引擎.取鼠标滚轮() > 0 then
		self.加入 = (self.加入 - 1) < 0 and 0 or self.加入 - 1
	elseif 引擎.取鼠标滚轮() < 0 then
		self.加入 = (self.加入 + 1) > (#self.自动数据 - 1) and #self.自动数据 - 1 or self.加入 + 1
		if #self.自动数据 == 1 then
			self.加入 = 0
		end
	end
	for n = 1, #self.自动数据 >= 1 and 1 or #self.自动数据 do
		local 序列 = self.加入 + n
		local 差距y = n * 160 - 160 - 55
		zts:置颜色(黄色)
		zts:显示(self.x + 15, self.y + 80 + 差距y,
			self.自动数据[序列][1].名称 ..
			"：" ..
			(self.自动数据[序列][1].指令 and ((self.自动数据[序列][1].指令.类型 == "法术" and self.自动数据[序列][1].指令.参数) or (self.自动数据[序列][1].指令.类型 == "攻击" and "攻击") or (self.自动数据[序列][1].指令.类型 == "防御" and "防御") or "未设置") or "未设置"))
		zts:显示(self.x + 140, self.y + 80 + 差距y,
			self.自动数据[序列][2] and
			self.自动数据[序列][2].名称 ..
			"：" ..
			(self.自动数据[序列][2].指令 and ((self.自动数据[序列][2].指令.类型 == "法术" and self.自动数据[序列][2].指令.参数) or (self.自动数据[序列][2].指令.类型 == "攻击" and "攻击") or (self.自动数据[序列][2].指令.类型 == "防御" and "防御") or "未设置") or "未设置") or
			"暂未参战")
		self.资源组[7]:显示(self.x + 10, self.y + 100 + 差距y)
		self.人物技能[序列]:置坐标(self.x + 11, self.y + 101 + 差距y)
		self.人物技能[序列]:显示(x, y, self.鼠标)
		if self.人物技能[序列] and (self.人物技能[序列].事件 or (self.资源组[7]:是否选中(x, y) and 引擎.鼠标弹起(0))) then
			战斗类.战斗指令.法术界面:打开(self.自动数据[序列][1].技能, "法术", "人物", nil, { 1, self.自动数据[序列][1].id, self.自动数据[序列][1].位置 })
			tp.鼠标.置鼠标("普通")
		end
		self.资源组[14 + n * 6 - 6]:更新1(x, y)
		self.资源组[15 + n * 6 - 6]:更新1(x, y)
		self.资源组[13 + n * 6 - 6]:更新1(x, y)
		self.资源组[14 + n * 6 - 6]:显示(self.x + 65, self.y + 98 + 差距y)
		self.资源组[15 + n * 6 - 6]:显示(self.x + 65, self.y + 123 + 差距y)
		self.资源组[13 + n * 6 - 6]:显示(self.x + 100 + 70, self.y + 150 + 差距y)
		zts:显示(self.x + 15, self.y + 95 + 3, "当前状态：")
		local 当前状态 = self.自动数据[序列][1].自动战斗 and " 自动中" or " 手动中"
		zts:显示(self.x + 80, self.y + 95 + 4, 当前状态)
		self.资源组[13 + n * 6 - 6]:置文字(self.自动数据[序列][1].自动战斗 and " 手动" or " 自动")
		if self.资源组[14 + n * 6 - 6]:事件判断() then
			local 结果 = 发送数据(5601, { 类型 = "攻击", 目标 = self.自动数据[序列][1].位置, 敌我 = 0, 参数 = "", 附加 = "", 玩家id = self.自动数据[序列][1].id, 操作类型 = 1 })
			if 结果 and 结果[1] and 结果[1].结果 then
				if 结果[1].结果.成功 then
					self.状态 = "攻击"
					-- 不显示提示消息
				else
					-- 不显示提示消息
				end
			end
		elseif self.资源组[15 + n * 6 - 6]:事件判断() then
			local 结果 = 发送数据(5601, { 类型 = "防御", 目标 = self.自动数据[序列][1].位置, 敌我 = 0, 参数 = "", 附加 = "", 玩家id = self.自动数据[序列][1].id, 操作类型 = 1 })
			if 结果 and 结果[1] and 结果[1].结果 then
				if 结果[1].结果.成功 then
					self.状态 = "防御"
					-- 不显示提示消息
				else
					-- 不显示提示消息
				end
			end
		elseif self.资源组[13 + n * 6 - 6]:事件判断() then
			local 结果 = 发送数据(5602, { 位置 = self.自动数据[序列][1].位置, 玩家id = self.自动数据[序列][1].id, 操作类型 = 1 })
			if 结果 and 结果[1] and 结果[1].结果 then
				if 结果[1].结果.成功 then
					self.状态 = self.自动数据[序列][1].自动战斗 and "自动" or "手动"
					-- 不显示提示消息
				else
					-- 不显示提示消息
				end
			end
		end
		self.资源组[7]:显示(self.x + 135, self.y + 100 + 差距y)
		self.宝宝技能[序列]:置坐标(self.x + 136, self.y + 101 + 差距y)
		self.宝宝技能[序列]:显示(x, y, self.鼠标)

		if self.自动数据[序列][2] and self.宝宝技能[序列] and (self.宝宝技能[序列].事件 or (self.资源组[7]:是否选中(x, y) and 引擎.鼠标弹起(0))) then
		    local 宠物技能目标 = self.自动数据[序列][2]
		    战斗类.战斗指令.法术界面:打开(宠物技能目标.技能, "法术", "宠物", nil, { 2, 宠物技能目标.id, 宠物技能目标.位置 })
		    tp.鼠标.置鼠标("普通")
		end
		self.资源组[17 + n * 6 - 6]:更新1(x, y)
		self.资源组[18 + n * 6 - 6]:更新1(x, y)
		self.资源组[16 + n * 6 - 6]:更新1(x, y)
		self.资源组[17 + n * 6 - 6]:显示(self.x + 190, self.y + 98 + 差距y)
		self.资源组[18 + n * 6 - 6]:显示(self.x + 190, self.y + 123 + 差距y)
		if self.资源组[17 + n * 6 - 6]:事件判断() and self.自动数据[序列][2] then
			local 宠物目标 = self.自动数据[序列][2].位置
			local 结果 = 发送数据(5601, { 类型 = "攻击", 目标 = 宠物目标, 敌我 = 0, 参数 = "", 附加 = "", 玩家id = self.自动数据[序列][2].id, 操作类型 = 2 })
			if 结果 and 结果[1] and 结果[1].结果 then
				if 结果[1].结果.成功 then
					-- 不显示提示消息
				else
					-- 不显示提示消息
				end
			end
		elseif self.资源组[18 + n * 6 - 6]:事件判断() and self.自动数据[序列][2] then
			local 结果 = 发送数据(5601, { 类型 = "防御", 目标 = self.自动数据[序列][2].位置, 敌我 = 0, 参数 = "", 附加 = "", 玩家id = self.自动数据[序列][2].id, 操作类型 = 2 })
			if 结果 and 结果[1] and 结果[1].结果 then
				if 结果[1].结果.成功 then
					-- 不显示提示消息
				else
					-- 不显示提示消息
				end
			end
		end
	end
	if #self.自动数据 > 1 then
		zts:显示(self.x + 15, self.y + 340 - 165 - 55, "操作总数:" .. (#self.自动数据) .. "个  当前操作的为:第" .. (self.加入 + 1) .. "个")
		zts:置颜色(白色)
		zts:显示(self.x + 37, self.y + 355 - 165 - 53, "滑动鼠标滚轮,可操作其他操作对象")
		zts:显示(self.x + 37, self.y + 355 - 165 - 53, "滑动鼠标滚轮,可操作其他操作对象")
	end
	战斗类.战斗指令.法术界面:显示(dt, x, y)
	if self.鼠标 then
	end
	if tp.按钮焦点 then
		self.焦点 = true
	end
	self.资源组[2]:更新1(x, y)

	if ((引擎.鼠标弹起(1) and not tp.禁止关闭 ) or self.资源组[2]:事件判断()) and self.鼠标 then
		--if self.状态 == " 取消" then
			-- 战斗结束时保存位置
			self:保存位置配置()
			发送数据(5507)
			战斗类.自动开关 = false
			return
		--end
		--战斗类.自动开关 = false
		--return
	end
end

function 场景类_战斗自动栏:刷新数据()
    -- 检查战斗类和自动数据是否存在
    if not 战斗类 or not 战斗类.自动数据 then
        return -- 如果战斗类或自动数据不存在，则直接返回
    end

    -- 初始化自动数据表
    self.自动数据 = {}

    -- 重置滚动位置，避免显示错位
    if self.加入 and self.加入 >= #战斗类.自动数据 then
        self.加入 = math.max(0, #战斗类.自动数据 - 1)
    end

    -- 遍历战斗类的自动数据
    for n = 1, #战斗类.自动数据 do
        local 临时数据 = 战斗类.自动数据[n]
        if 临时数据 then
            self.自动数据[n] = {}

            -- 处理角色数据
            if 临时数据[1] then
                self.自动数据[n][1] = 临时数据[1]
                local x = 引擎.取头像(临时数据[1].模型)
                if x and x[7] then
                    self.自动数据[n][1].头像 = tp.资源:载入(x[7], "网易WDF动画", x[2] or 0)
                end
                self.自动数据[n][1][51] = tp.资源:载入('wzife.wdf', "网易WDF动画", 0xAAD44583)
                self.自动数据[n][1][52] = tp.资源:载入('wzife.wdf', "网易WDF动画", 0xCE4D3C2D)
                self.自动数据[n][1][53] = tp.资源:载入('wzife.wdf', "网易WDF动画", 0xBAF8009F)

                self.人物技能[n]:置技能()
                if 临时数据[1].指令 and 临时数据[1].指令.类型 == "法术" then
                    self.人物技能[n]:置技能({ 名称 = 临时数据[1].指令.参数 })
                end
            end

            -- 处理宠物数据
            if 临时数据[2] then
                self.自动数据[n][2] = 临时数据[2]
                local x = 引擎.取头像(临时数据[2].模型)
                if x and x[7] then
                    self.自动数据[n][2].头像 = tp.资源:载入(x[7], "网易WDF动画", x[1] or 0)
                end
                self.自动数据[n][2][51] = tp.资源:载入('wzife.wdf', "网易WDF动画", 0xAAD44583)
                self.自动数据[n][2][52] = tp.资源:载入('wzife.wdf', "网易WDF动画", 0xCE4D3C2D)


                self.宝宝技能[n]:置技能()
                if 临时数据[2].指令 and 临时数据[2].指令.类型 == "法术" then
                    self.宝宝技能[n]:置技能({ 名称 = 临时数据[2].指令.参数 })
                end
            end
        end
    end

    -- 动态调整窗口高度
    local 高度 = 180 - 55
    if #self.自动数据 > 1 then
        高度 = 高度 + 35
    end
    for n = 2, #self.自动数据 do
        高度 = 高度 + 105
        if 高度 >= 155 then
            高度 = 155
            break
        end
    end
    self.资源组[1] = tp._自适应(0, 1, 255, 高度, 3, 9)

    -- 调试信息：显示最终处理后的数据数量
    print("自动栏最终数据数量:", #self.自动数据)
end

function 场景类_战斗自动栏:检查点(x,y)
    if UI切换状态 == "简化" then
        -- 简化模式下检查简化背景区域，根据文字计算尺寸
        local 文字宽度 = tp.字体表.华康字体:取宽度("自动战斗开启中")
        local 背景宽度 = 文字宽度 + 30
        local 背景高度 = 22
        if x >= self.x and x <= self.x + 背景宽度 and y >= self.y and y <= self.y + 背景高度 then
            return true
        end
    else
        -- 完整模式下检查完整UI区域
        if self.资源组[1]:是否选中(x,y) then
            return true
        end
    end
    return false
end

function 场景类_战斗自动栏:初始移动(x,y)
    tp.运行时间 = tp.运行时间 + 1
    if not self.第一次 then
        tp.运行时间 = tp.运行时间 + 2
        self.第一次 = true
    end
    if not 引擎.场景.消息栏焦点 then
        self.窗口时间 = tp.运行时间
    end
    if not self.焦点 then
        tp.场景.战斗.移动窗口 = true
    end
    if self.可视化 and self.鼠标 and not self.焦点 then
        self.xx = x - self.x
        self.yy = y - self.y
    end
end
function 场景类_战斗自动栏:开始移动(x, y)
    if self.鼠标 then
        self.x = x - self.xx
        self.y = y - self.yy
        自动战斗栏位置.x = self.x -- 更新全局存储的坐标
        自动战斗栏位置.y = self.y -- 更新全局存储的坐标
    end
end



-- 保存位置配置
function 场景类_战斗自动栏:保存位置配置()
    写配置(配置文件路径, "mhxy", "自动战斗栏位置X", tostring(self.x))
    写配置(配置文件路径, "mhxy", "自动战斗栏位置Y", tostring(self.y))
    --print("战斗自动栏位置已保存: X=" .. self.x .. ", Y=" .. self.y)
end

return 场景类_战斗自动栏
