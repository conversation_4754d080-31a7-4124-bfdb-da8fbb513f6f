-- @Author: 小九呀丶 - QQ：5268416
-- @Date:   2025-03-05 14:35:42
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-05-31 15:07:15

local 帮派迷宫 = class()
function 帮派迷宫:初始化()
	self.数据={}
	self.活动开关=false
	self.激活成功=false
	self.刷出道人=false
	self.宝箱开关=false
	self.活动时间=QJHDSJ["帮派迷宫"]
	self:加载公共数据()
end
function 帮派迷宫:活动定时器()
	if self.活动开关 then
		if self.激活成功~=false then
			if self.开启Time-os.time()>0 then
				if self.宝箱开关==false then
					if self.开启Time-os.time()<605 then 
						self.宝箱Time=os.time()+300
						self.宝箱开关=true
						发送公告("#Y各位玩家请注意，帮派迷宫宝箱将在#R5#Y分钟后刷出，此次宝箱刷新10轮，#R还在战斗中的玩家，战斗结束后#Y，请玩家到#P【清风密林】#Y耐心等待")
						广播消息({内容="#Y各位玩家请注意，帮派迷宫宝箱将在#R5#Y分钟后刷出，此次宝箱刷新10轮，#R还在战斗中的玩家，战斗结束后#Y，请玩家到#P【清风密林】#Y耐心等待。",频道="hd"})
						广播消息({内容="#Y各位玩家请注意，帮派迷宫宝箱将在#R5#Y分钟后刷出，此次宝箱刷新10轮，#R还在战斗中的玩家，战斗结束后#Y，请玩家到#P【清风密林】#Y耐心等待。",频道="bp"})
					end
				else
					if self.宝箱Time-os.time()<0 then
						self:刷出宝箱()
						self.宝箱Time=os.time()+60
					end
				end
			else
				self:关闭活动()
			end
		end
	else
		if self.活动时间.日期=="每天" then
			if self.活动时间.时==服务端参数.小时+0 and self.活动时间.分==服务端参数.分钟+0 and self.活动时间.秒<=服务端参数.秒+0  then
				self:开启活动()
			end
		else
			local zhouji=tonumber(os.date("%w", os.time()))
			if zhouji==self.活动时间.日期 then
				if self.活动时间.时==服务端参数.小时+0 and self.活动时间.分==服务端参数.分钟+0 and self.活动时间.秒<=服务端参数.秒+0  then
					self:开启活动()
				end
			elseif self.活动时间.日期=="周135" and (zhouji==1 or zhouji==3 or zhouji==5) then
				if self.活动时间.时==服务端参数.小时+0 and self.活动时间.分==服务端参数.分钟+0 and self.活动时间.秒<=服务端参数.秒+0  then
					self:开启活动()
				end
			elseif self.活动时间.日期=="周246" and (zhouji==2 or zhouji==4 or zhouji==6) then
				if self.活动时间.时==服务端参数.小时+0 and self.活动时间.分==服务端参数.分钟+0 and self.活动时间.秒<=服务端参数.秒+0  then
					self:开启活动()
				end
			end
		end
	end
end
function 帮派迷宫:开启活动()
	self.活动开关=true
	self.开启Time=nil
	self.宝箱Time=nil
	self.激活成功=false
	self.刷出道人=false
	self.宝箱开关=false
	发送公告("#G(帮派迷宫)#P已经开启，各帮帮主可以通过帮派土地公公报名了！#G迷宫之主#P名额只有一个！！")
	广播消息({内容=format("#G(帮派迷宫)#P已经开启，各帮帮主可以通过帮派土地公公报名了！#G迷宫之主#P名额只有一个！！#84"),频道="hd"})
end
function 帮派迷宫:激活活动开关()
	self.开启Time=os.time()+7200
	self:刷出入侵者()
	self:秽气妖()
	self:秽气妖()
	self:三才()
end
function 帮派迷宫:关闭活动(id) 
	self.数据={}
	self.活动开关=false
	self.开启Time=nil
	self.宝箱Time=nil
	self.激活成功=false
	self.刷出道人=false
	self.宝箱开关=false
	广播消息({内容="#G(帮派迷宫)#Y活动结束，感谢大家的参与，#R3#Y分钟后将自动传送出#P【清风密林】#Y。",频道="xt"})
end
function 帮派迷宫:怪物对话内容(id,类型,标识,地图)
	local 对话数据={}
	对话数据.模型=任务数据[标识].模型
	对话数据.名称=任务数据[标识].名称
	local 名称 = 任务数据[标识].名称
	if 名称=="入侵者" then
		if 任务数据[标识].战斗 then
			对话数据.对话="我正在战斗中，请勿打扰。"
			return 对话数据
		end
		对话数据.对话="哈哈哈，真可笑，这块宝地和宝藏很快就是我们的了，何来入侵之说？"
		对话数据.选项={"既然如此，我只好打死你了","算了，算了放你一马。"}
	elseif 名称=="秽气妖" then
		if 任务数据[标识].战斗 then
			对话数据.对话="我正在战斗中，请勿打扰。"
			return 对话数据
		end
		对话数据.对话="想知道阵法入口在哪里？打死我也不会告诉你的#86"
		对话数据.选项={"既然如此，我只好打死你了","算了，算了放你一马。"}
	elseif 类型==1301 then
		if 任务数据[标识].地图编号==1230 then 
			if self.数据[id] and self.数据[id].三才阵眼资格~=nil then
				对话数据.对话="你已经取得了破阵的资格，请在此地图逐个破阵"
			else
				对话数据.对话="劳释道人再迷宫内布下阵法，断绝水源，妄想杀死灵木宝树、独吞宝藏。为了帮派大计，我们必须阻止他，宝藏终将属于我们！"
				对话数据.选项={"我要前去破阵","我想回到地面上（清风密林）","为何要为五斗米折腰"}
			end
		elseif 任务数据[标识].地图编号==2002 then 
			对话数据.对话="劳释道人在前方布下天地三才阵，封住了前往地底水源的通道。诸位助我掠阵，若能击杀#Y阵眼#W，击杀会更快！"
			对话数据.选项={"让我去会会劳释道人","我想回到地面上（清风密林）","我想听听阵法玄妙","为何要为五斗米折腰"}
		end
	elseif 类型==1304 then 
		if 任务数据[标识].战斗 then
			对话数据.对话="我正在战斗中，请勿打扰。"
			return 对话数据
		end
		对话数据.对话="三才者，天地人。三光者，日月星#112"
		对话数据.选项={"我也很强，点的就是你。","这是什么感觉，好强的压迫感！"}
	elseif 类型==1305 then 
		对话数据.对话="真身几何，天知、地知、人知、你不知！#71"
		对话数据.选项={"你在叽里呱啦说些什么？看拳！","等会再来会会你"}
	elseif 名称=="金龙" then
		if 任务数据[标识].战斗 then
			对话数据.对话="我正在战斗中，请勿打扰。"
			return 对话数据
		end
		对话数据.对话="我是金龙，迷宫之主的专属#28！我在这里只为等待迷宫之主的邂逅#2"
		对话数据.选项={"终于找到你了","好强的压迫感！"}
	elseif 名称=="祈愿宝箱" then
		对话数据.对话="这是为迷宫之主特意准备的一点小心意，快打开看看里面有什么好东西吧#1"
		对话数据.选项={"确定开启","还是算了吧"}
	elseif 类型==1306 then
		对话数据.对话="区区小帮，竟然与我抗衡，识相的快点消失，否则让你们灭帮。"
		对话数据.选项={"该消失的是你，宝藏我们替你收下了！","噢，这样啊，我们马上消失。"}
	elseif 类型==1307 then
		if 任务数据[标识].战斗==nil then
			对话数据.对话="今日我要剿灭灵宝神树，夺取宝箱，此事与你们无关，识相的快点消失，否则让你们灭帮。"
			对话数据.选项={"该消失的是你，宝藏我们替你收下了！","噢，这样啊，我们马上消失。"}
		else
			对话数据.对话="我正在战斗中请勿打扰"
		end
	elseif 类型==1310 then
		if 名称=="金宝箱" then
			对话数据.对话="你确定要使用金钥匙开启金宝箱吗？（相应的宝箱，对应相应的钥匙方可开启）"
			对话数据.选项={"确定开启金宝箱","还是算了吧"}
		elseif 名称=="银宝箱" then
			对话数据.对话="你确定要使用银钥匙开启银宝箱吗？（相应的宝箱，对应相应的钥匙方可开启）"
			对话数据.选项={"确定开启银宝箱","还是算了吧"}
		elseif 名称=="铜宝箱" then
			对话数据.对话="你确定要使用铜钥匙开启铜宝箱吗？（相应的宝箱，对应相应的钥匙方可开启）"
			对话数据.选项={"确定开启铜宝箱","还是算了吧"}
		end
	end
	return 对话数据
end
function 帮派迷宫:怪物对话(id,名称,事件,任务id,类型)
	if 事件=="既然如此，我只好打死你了" then
		if 任务数据[任务id].战斗~=nil then 常规提示(id,"#Y/对方正在战斗中") return  end
		if 玩家数据[id].队伍==0 then 常规提示(id,"#Y/需要组队才能触发") return  end
		if 名称=="入侵者" then
			if self.数据[id].入侵者次数<4 then
				战斗准备类:创建战斗(id,130053,任务id)
				任务数据[任务id].战斗=true
			else
				添加最后对话(id,"打完还鞭尸，你是真的无情#52")
			end
		elseif 名称=="秽气妖" then
			战斗准备类:创建战斗(id,130054,任务id)
			任务数据[任务id].战斗=true
		end
	elseif 事件=="该消失的是你，宝藏我们替你收下了！" then
		if 玩家数据[id].队伍==0 then 常规提示(id,"#Y/需要组队才能触发") return  end
		if 名称=="劳释道人" then
			if self.数据[id].劳释道人==nil then
				战斗准备类:创建战斗(id,130061,任务id)
				任务数据[任务id].战斗=true
			else
				添加最后对话(id,"一人只能杀一次")
			end
		elseif 名称=="秽气源头" then
			if 任务数据[任务id].战斗~=nil then 常规提示(id,"#Y/对方正在战斗中") return  end
			战斗准备类:创建战斗(id,130062,任务id)
			任务数据[任务id].战斗=true
		end
	elseif 事件=="我要前去破阵" then
		if self.数据[id].秽气妖次数>=10 then
			if self.数据[id].三才阵眼资格 then
				添加最后对话(id,"你的阵眼已经刷新在这个地图了，抓紧时间去破阵")
			else
				self:三才阵眼(id)
				self:设置任务(id,3)
				self.数据[id].三才阵眼资格=1
				local 队伍id=玩家数据[id].队伍 
				if 队伍id~=0 then
					for n=1,#队伍数据[队伍id].成员数据 do
						local cyid=队伍数据[队伍id].成员数据[n]
						if self.数据[cyid]==nil then
							self.数据[cyid]={入侵者次数=0,秽气妖次数=0}
						end
						self.数据[cyid].三才阵眼资格=1
					end
				end
				添加最后对话(id,"我果然没有看走眼，帮中若是多些你这样的人才，何愁霸业不成！且随我去破阵吧。#R在这个地图击杀你的三才阵眼")
				常规提示(id,"#G你的阵眼刷新了，快快前去破阵！")
			end
		else
			添加最后对话(id,"还是先杀够10只秽气妖来证明你的实力才行啊#14,当前你已经击杀了:#G"..self.数据[id].秽气妖次数.."#W只")
		end
	elseif 事件=="让我去会会劳释道人" then
		if self.数据[id].地才~=nil and self.数据[id].天才~=nil and self.数据[id].人才~=nil then
			if self.刷出道人==false then
				self.刷出道人=true
				self:劳释道人()
				self:秽气源头()
			end
			self:设置任务(id,6)
			添加最后对话(id,"我果然没有看走眼，帮中若是多些你这样的人才，何愁霸业不成！且随我去会会劳释道人！。")
			常规提示(id,"我果然没有看走眼，帮中若是多些你这样的人才，何愁霸业不成！且随我去会会劳释道人！。")
		else
			添加最后对话(id,"先破了这层的三才阵！赶紧前往掠阵吧！")
		end
	elseif 事件=="我想回到地面上（清风密林）" then
		地图处理类:跳转地图(id,2003,103,83)
	elseif 事件=="我也很强，点的就是你。" then
		if 任务数据[任务id].归属~=id+0 then
			添加最后对话(id,"这不是你的阵眼，请勿捣乱！")
			return
		end
		if 名称=="天才阵眼" then
			战斗准备类:创建战斗(id,130055,任务id)
			任务数据[任务id].战斗=true
		elseif 名称=="地才阵眼" then
			战斗准备类:创建战斗(id,130056,任务id)
			任务数据[任务id].战斗=true
		elseif 名称=="人才阵眼" then
			战斗准备类:创建战斗(id,130057,任务id)
			任务数据[任务id].战斗=true
		end
	elseif 事件=="你在叽里呱啦说些什么？看拳！" then
		if 名称=="天才" then
			if self.数据[id].天才==nil then
				战斗准备类:创建战斗(id,130058,任务id)
				任务数据[任务id].战斗=true
			else
				添加最后对话(id,"你已经过了我这关了，想办法解决下一关吧。")
			end
		elseif 名称=="地才" then
			if self.数据[id].地才==nil then
				战斗准备类:创建战斗(id,130059,任务id)
				任务数据[任务id].战斗=true
			else
				添加最后对话(id,"你已经过了我这关了，想办法解决下一关吧。")
			end
		elseif 名称=="人才" then
			if self.数据[id].人才==nil then
				战斗准备类:创建战斗(id,130060,任务id)
				任务数据[任务id].战斗=true
			else
				添加最后对话(id,"你已经过了我这关了，想办法解决下一关吧。")
			end
		end
	elseif 事件=="终于找到你了" then
		if 任务数据[任务id].迷宫之主==id then
			战斗准备类:创建战斗(id,130063,任务id)
			任务数据[任务id].战斗=true
		else
			添加最后对话(id,"#55你是迷宫之主吗")
		end
	elseif 名称=="祈愿宝箱" and 事件=="确定开启" then 
		if 任务数据[任务id].迷宫之主==id then
			玩家数据[id].道具:给予道具(id,"祈愿宝箱",取随机数(1,3))
			地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].单位编号)
			任务数据[任务id]=nil
		else
			添加最后对话(id,"只有迷宫之主才能打开#111")
		end
	elseif 类型==1310 then
		if 事件=="确定开启金宝箱" then
			self:开宝箱(id,"金钥匙",任务id)
		elseif 事件=="确定开启银宝箱" then
			self:开宝箱(id,"银钥匙",任务id)
		elseif 事件=="确定开启铜宝箱" then
			self:开宝箱(id,"铜钥匙",任务id)
		end
	end
end
function 帮派迷宫:对话事件处理(id,名称,事件,类型)
	if 类型 then
		if self.数据[id] or 类型==1310 then
			local 任务id=玩家数据[id].地图单位.标识
			if 任务数据[任务id]==nil then
				添加最后对话(id,"这只是个虚影")
				return
			end
			self:怪物对话(id,名称,事件,任务id,类型)
		else
			添加最后对话(id,"现在不是活动时间呀")
		end
		return
	end
	if 名称=="帮派土地公公" then
		if 事件=="我要成为帮派迷宫之主" then
			if not self.活动开关  then 
				添加最后对话(id,"当前未到活动时间！在活动时间内，如果你是一帮之主，你可以来找我开启帮派迷宫活动~")
				return
			elseif 玩家数据[id].队伍==0 and 玩家数据[id].队长==false then
				添加最后对话(id,"这种重要的事情还是让队长来吧！")
				return
			end
			local bh=玩家数据[id].角色.BPBH
			if 帮派数据[bh] then
				if 帮派数据[bh].成员数据[id].权限<4 then
					添加最后对话(id,"你没有足够的权限来开启迷宫，还是叫帮主或副帮主来吧")
					return
				elseif self.激活成功~=false then
					添加最后对话(id,"今日的帮派迷宫已被#Y"..self.激活成功.帮派.."#W帮派开启了，请勿重复开启！")
					return
				end
				self:激活活动开关()
				self.激活成功={帮派=帮派数据[bh].帮派名称,编号=bh,开启id=id,开启者=玩家数据[id].角色.名称}
				self:迷宫之主(id)
				self:金龙(id)
				常规提示(id,"#G你成功开启了帮派迷宫！")
				广播所有帮派消息({内容="#P(帮派迷宫)#Y帮派迷宫已经开启#W大批入侵者出现在了猴族的家园，击杀每个入侵者可获得25点信任度，获取#G100点信任度#W后，回去点猴爷爷即能进入地下迷宫开启探险之旅。#84",频道="bp"})
				广播消息({内容=format("#P(帮派迷宫)#W【#Y%s#W】的#Y%s#W已经成功的开启了帮派迷宫，欢迎大家踊跃参加！#87",帮派数据[bh].帮派名称,玩家数据[id].角色.名称),频道="hd"})
				帮派处理:增加当前内政(bh,1000,"提示")
			end
		elseif 事件=="我要进入帮派迷宫" then
			if self.激活成功==false then
				添加最后对话(id,"当前未到活动时间或没有帮派开启！如果某个帮派开启了帮派迷宫，你才能进入帮派迷宫！")
				return
			elseif not 调试模式 and (玩家数据[id].队伍==0 or 取队伍人数(id)<3 or 取队伍最低等级(玩家数据[id].队伍,40) ) then
				添加最后对话(id,"帮派迷宫参与条件：≥40级，≥3人")
				return
			elseif 玩家数据[id].队伍==0 and 玩家数据[id].队长==false then
				添加最后对话(id,"这种重要的事情还是让队长来吧！")
				return
			end
			if self.数据[id]==nil then
				self:刷出入侵者()
			end
			self:检查队伍数据(id)
			添加最后对话(id,"迷宫内危险重重，少侠可要注意安全啊！")
			地图处理类:跳转地图(id+0,2003,92,71)
		end
	elseif 名称 == "猴爷爷" then
		if 事件=="进入地下迷宫" then
			if self.数据[id]==nil then
				添加最后对话(id,"现在不是活动时间呀")
				return
			elseif self.数据[id].入侵者次数<4 then
				添加最后对话(id,"你当前队伍的信任度不足100".."，目前你的信任度为#G"..self.数据[id].入侵者次数*25)
				return
			elseif not 调试模式 and (玩家数据[id].队伍==0 or 取队伍人数(id)<3 or 取队伍最低等级(玩家数据[id].队伍,40) ) then
				添加最后对话(id,"帮派迷宫参与条件：≥40级，≥3人")
				return
			elseif 玩家数据[id].队长==false then
				添加最后对话(id,"这种重要的事情还是让队长来吧！")
				return
			end
			local rw1=玩家数据[id].角色:取任务(1180)
			if rw1==0 then
				self:设置任务(id,1)
			end
			地图处理类:跳转地图(id,1230,22,22)
		end
	elseif 名称 == "灵木宝树" then
		if 事件=="我想进入无源之水" then
			if self.数据[id] and self.数据[id].天才阵眼 and self.数据[id].地才阵眼 and self.数据[id].人才阵眼 then
				地图处理类:跳转地图(id,2002,20,20)
				local rw1=玩家数据[id].角色:取任务(1180)
				if 任务数据[rw1] and 任务数据[rw1].进程==4 then
					self:设置任务(id,5)
				end
			else
				添加最后对话(id,"你得找迷宫之主破了阵眼，我才放心让你进去！")
			end
		elseif 事件=="我想去清风密林" then
			地图处理类:跳转地图(id+0,2003,92,71)
		end
	end
end
function 帮派迷宫:任务说明(玩家id,任务id)
	local 说明 = {}
	if not self.活动开关 then
		说明={"帮派迷宫",format("活动已经结束。")}
		return 说明
	elseif not self.数据[玩家id] then
		return {"帮派迷宫",format("暂无任务说明。")}
	end
	local 进程=任务数据[任务id].进程
	if 进程==1 then
		说明={"讨伐秽气妖",format("此处秽气弥漫，先消灭10只秽气妖，再与#Y（秽气巢林，19，19）#W/的迷宫之主#Y%s#W商议对策。已击杀#Y%s-10#W只秽气妖。",self.激活成功.开启者,self.数据[玩家id].秽气妖次数)}
	elseif 进程==2 then
		说明={"阵眼杀手",format("找到#Y（秽气巢林，19，19）#W/的迷宫之主#Y%s#W激活阵眼。",self.激活成功.开启者)}
	elseif 进程==3 then
		local sl=0
		for k,v in pairs(self.数据[玩家id]) do
			if k=="地才阵眼" then
				sl=sl+1
			end
			if k=="人才阵眼" then
				sl=sl+1
			end
			if k=="天才阵眼" then
				sl=sl+1
			end
		end
		说明={"阵眼杀手",format("击杀#Y3只天、地、人才阵眼#W，将获得一枚迷宫钥匙！获得前往干涸的无源之水资格。目前已击杀#Y%s#W只。",sl)}
	elseif 进程==4 then
		说明={"天地三才阵",format("#Y灵宝神木(秽气巢林,133,86)#W苏醒了，通过它进入“干涸的无源之水”。",self.激活成功.开启者)}
	elseif 进程==5 then
		说明={"天地三才阵",format("击杀天、地、人三才各#Y三重身#W，详情可向#Y%s(无源之水,43,29)#W讨教。",self.激活成功.开启者)}
	elseif 进程==6 then
		说明={"诛杀劳释道人",format("找到#Y（无源之水，19，19）#W/的迷宫之主#Y%s#W，前往消灭#Y劳释道人(无源之水,107,83)#W。",self.激活成功.开启者)}
	elseif 进程==7 then
		说明={"肃清余孽",format("劳释道人已被打跑，却在地底留下了#Y秽气源头#W。赶在水源恢复之前尽快清除吧！")}
	end
	return 说明
end
function 帮派迷宫:设置任务(id,jincheng)
	local rw1=玩家数据[id].角色:取任务(1180)
	local 队伍id=玩家数据[id].队伍
	if rw1==0 then
		local 任务id,ZU=取唯一任务(1180,id)
		任务数据[任务id]={
		领取人id=id,
		id=任务id,
		玩家id=id,
		DWZ=ZU,
		销毁=true,
		起始=os.time(),
		结束=7200,
		进程=jincheng,
		类型=1180
		}
		for n=1,#队伍数据[队伍id].成员数据 do
			local 成员id=队伍数据[队伍id].成员数据[n]
			玩家数据[成员id].角色:取消任务(玩家数据[成员id].角色:取任务(1180)) 
			玩家数据[成员id].角色:添加任务(任务id)
		end
	else
		任务数据[rw1].进程=jincheng
		for n=1,#队伍数据[队伍id].成员数据 do
			local 成员id=队伍数据[队伍id].成员数据[n]
			if 玩家数据[成员id].角色:取任务(1180)==0 then
				玩家数据[成员id].角色:添加任务(rw1)
			else
				local rw3=玩家数据[成员id].角色:取任务(1180)
				if rw3~=rw1 then
					玩家数据[成员id].角色:取消任务(rw3) 
					玩家数据[成员id].角色:添加任务(rw1)
				end
			end
			玩家数据[成员id].角色:刷新任务跟踪()
		end
	end
end
function 帮派迷宫:检查队伍数据(id)
	if self.激活成功~=false then
		local 队伍id=玩家数据[id].队伍
		if 队伍id~=0 then
			for n=1,#队伍数据[队伍id].成员数据 do
				local cyid=队伍数据[队伍id].成员数据[n]
				if self.数据[cyid]==nil then
					self.数据[cyid]={入侵者次数=0,秽气妖次数=0}
				end
			end
		else
			if self.数据[id]==nil then
				self.数据[id]={入侵者次数=0,秽气妖次数=0}
			end
		end
	end
end
function 帮派迷宫:迷宫之主(id)
	local 地图范围={1230,2002}
	local xy={{x=18,y=18},{x=43,y=27}}
	local 装备,武器子类,武器等级,武器染色方案,武器染色组
	local wq=玩家数据[id].角色.装备[3]
	if 玩家数据[id].道具.数据[wq]~=nil then
		装备 = 玩家数据[id].道具.数据[wq].名称
		武器子类 = 玩家数据[id].道具.数据[wq].子类
	end
	for i=1,2 do
		local 地图=地图范围[i]
		local dtmc=取地图名称(地图)
		local 任务id=取唯一任务(1301)
		任务数据[任务id]={
		id=任务id,
		销毁=true,
		起始=os.time(),
		结束=7200,
		x=xy[i].x,
		y=xy[i].y,
		地图编号=地图,
		地图名称=dtmc,
		称谓="迷宫之主",
		名称=玩家数据[id].角色.名称,
		模型=玩家数据[id].角色.造型,
		染色组=玩家数据[id].角色.染色组,
		染色方案=玩家数据[id].角色.染色方案,
		武器=装备,
		武器子类 = 武器子类,
		方向=1,
		任务显示=true,
		类型=1301
		}
		地图处理类:添加单位(任务id)
	end
end
function 帮派迷宫:金龙(id)
	local 地图=2002
	local dtmc=取地图名称(地图)
	local 任务id=取唯一任务(1309)
	local xy=地图处理类.地图坐标[地图]:取随机点()
	任务数据[任务id]={
	id=任务id,
	销毁=true,
	起始=os.time(),
	结束=7200,
	名称="金龙",
	模型="蛟龙",
	称谓=玩家数据[id].角色.名称,
	迷宫之主=id,
	变异=true,
	x=xy.x,
	y=xy.y,
	地图编号=地图,
	地图名称=dtmc,
	类型=1309
	}
	地图处理类:添加单位(任务id)
end
function 帮派迷宫:刷出入侵者()
	local 地图=2003
	local dtmc=取地图名称(地图)
	for n=1,5 do
		local 任务id=取唯一任务(1302)
		local xy=地图处理类.地图坐标[地图]:取随机点()
		任务数据[任务id]={
		id=任务id,
		销毁=true,
		起始=os.time(),
		结束=1200,
		名称="入侵者",
		模型="山贼",
		行走开关=true,
		x=xy.x,
		y=xy.y,
		地图编号=地图,
		地图名称=dtmc,
		类型=1302
		}
		地图处理类:添加单位(任务id)
	end
end
function 帮派迷宫:秽气妖()
	local 地图=1230
	local dtmc=取地图名称(地图)
	for n=1,20 do
		local 任务id=取唯一任务(1303)
		local xy=地图处理类.地图坐标[地图]:取随机点()
		任务数据[任务id]={
		id=任务id,
		销毁=true,
		起始=os.time(),
		结束=3600,
		名称="秽气妖",
		模型="蜃气妖",
		x=xy.x,
		y=xy.y,
		地图编号=地图,
		地图名称=dtmc,
		类型=1303
		}
		地图处理类:添加单位(任务id)
	end
end
function 帮派迷宫:三才阵眼(id)
	local 地图=1230
	local dtmc=取地图名称(地图)
	local 阵眼={"天才阵眼","地才阵眼","人才阵眼"}
	for i=1,3 do
		local 任务id=取唯一任务(1304)
		local xy=地图处理类.地图坐标[地图]:取随机点()
		任务数据[任务id]={
		id=任务id,
		销毁=true,
		起始=os.time(),
		结束=3600,
		名称=阵眼[i],
		称谓=玩家数据[id].角色.名称,
		模型="阵眼",
		归属=id,
		x=xy.x,
		y=xy.y,
		地图编号=地图,
		地图名称=dtmc,
		类型=1304
		}
		地图处理类:添加单位(任务id)
	end
end
function 帮派迷宫:三才()
	local 地图=2002
	local dtmc=取地图名称(地图)
	local 三才={"天才","地才","人才"}
	local mx={"阴阳伞","机关人人形","修罗傀儡鬼"}
	local xy={{x=15,y=56},{x=99,y=38},{x=69,y=83}}
	for i=1,3 do
		local 任务id=取唯一任务(1305)
		任务数据[任务id]={
		id=任务id,
		销毁=true,
		起始=os.time(),
		结束=7200,
		变异=true,
		任务显示=true,
		名称=三才[i],
		模型=mx[i],
		x=xy[i].x,
		y=xy[i].y,
		地图编号=地图,
		地图名称=dtmc,
		类型=1305
		}
		地图处理类:添加单位(任务id)
	end
end
function 帮派迷宫:劳释道人()
	local 地图=2002
	local dtmc=取地图名称(地图)
	local 任务id=取唯一任务(1306)
	任务数据[任务id]={
	id=任务id,
	销毁=true,
	起始=os.time(),
	结束=7200,
	战斗显示=true,
	名称="劳释道人",
	模型="进阶雨师",
	x=107,
	y=83,
	地图编号=地图,
	地图名称=dtmc,
	类型=1306
	}
	地图处理类:添加单位(任务id)
end
function 帮派迷宫:秽气源头()
	local 地图=2002
	local dtmc=取地图名称(地图)
	for i=1,15 do
		local 任务id=取唯一任务(1307)
		local xy=地图处理类.地图坐标[地图]:取随机点()
		任务数据[任务id]={
		id=任务id,
		销毁=true,
		起始=os.time(),
		结束=3600,
		名称="秽气源头",
		模型="进阶蜃气妖",
		x=xy.x,
		y=xy.y,
		地图编号=地图,
		地图名称=dtmc,
		行走开关=true,
		类型=1307
		}
		地图处理类:添加单位(任务id)
	end
end
function 帮派迷宫:祈愿宝箱(id)
	local 地图=2003
	local dtmc=取地图名称(地图)
	local 任务id=取唯一任务(1308)
	任务数据[任务id]={
	id=任务id,
	销毁=true,
	起始=os.time(),
	迷宫之主=id,
	结束=7200,
	名称="祈愿宝箱",
	模型="大银宝箱",
	事件="可点击对话",
	x=44,
	y=24,
	地图编号=地图,
	地图名称=dtmc,
	类型=1308
	}
	地图处理类:添加单位(任务id)
end
function 帮派迷宫:刷出宝箱()
	local 地图=2003
	local dtmc=取地图名称(地图)
	for i=1,144 do
		local 任务id=取唯一任务(1310)
		local 几率=取随机数(1,1000)
		local 模型="金宝箱"
		local 名称="金宝箱"
		if 几率<=500 then
			模型="宝箱"
			名称="铜宝箱"
		elseif 几率<=780 then
			模型="赤金宝箱"
			名称="银宝箱"
		end
		任务数据[任务id]={
		id=任务id,
		销毁=true,
		起始=os.time(),
		结束=25,
		名称=名称,
		模型=模型,
		x=self.宝箱坐标[i].x,
		y=self.宝箱坐标[i].y,
		地图编号=地图,
		地图名称=dtmc,
		事件="可点击对话",
		类型=1310
		}
		地图处理类:添加单位(任务id)
	end
	广播消息({内容=format("#G帮派迷宫宝箱已刷出，带上你们的钥匙开启对应的宝箱吧".."#"..取随机数(1,110)),频道="hd"})
end
function 帮派迷宫:开祈愿宝箱(id)
	local 链接={提示=format("#G/%s#Y打开祈愿宝箱时，发现一个",玩家数据[id].角色.名称),频道="hd",结尾="#Y并将其收入囊中。"}
	local 奖励参数=取随机数(1,150)
	local 名称
	if 奖励参数<=1 then
		名称="神兜兜"
		玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
	elseif 奖励参数<=35 then
		名称="玲珑宝图"
		玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
	elseif 奖励参数<=45 then
		名称="高级魔兽要诀"
		玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
	elseif 奖励参数<=55 then
		名称="附魔宝珠"
		玩家数据[id].道具:给予超链接道具(id,名称,160,nil,链接,"成功")
	elseif 奖励参数<=60 then
		名称="特效宝珠"
		玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
	elseif 奖励参数<=75 then
		名称="修炼果"
		玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
	else
		玩家数据[id].角色:添加经验(math.floor(玩家数据[id].角色.等级*1500),"初识地宫")
		常规提示(id,"你获得了"..(玩家数据[id].角色.等级*1500).."点经验")
	end
end
function 帮派迷宫:开宝箱(id,物品,任务id)
	if 任务数据[任务id]==nil then
		常规提示(id,"这个宝箱被人抢先一步了……")
		return
	end
	if not 玩家数据[id].道具:判定背包道具(id,物品,1) then
		常规提示(id,"你没有"..物品.."无法开启此物品。")
		return
	end
	local 队伍id=玩家数据[id].队伍
	if 队伍id~=0 then
		for n=1,#队伍数据[队伍id].成员数据 do
			local cyid = 队伍数据[队伍id].成员数据[n]
			if id~=cyid and not 玩家数据[cyid].道具:判定背包道具(cyid,物品,1) then
				常规提示(id,"队员"..玩家数据[cyid].角色.名称.."没有"..物品.."无法开启此物品。")
				常规提示(cyid,"你没有"..物品.."无法开启此物品。")
				return
			end
		end
		地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].单位编号)
		任务数据[任务id]=nil
		local 链接
		if 物品=="金钥匙" then
			for n=1,#队伍数据[队伍id].成员数据 do
				local cyid = 队伍数据[队伍id].成员数据[n]
				if 玩家数据[cyid].道具:消耗背包道具(玩家数据[cyid].连接id,cyid,"金钥匙",1) then
					链接={提示=format("#S(帮派迷宫)#R/%s#Y开启金宝箱时获得了",玩家数据[cyid].角色.名称),频道="hd",结尾="#Y。"}
					local 奖励参数=取随机数(1,460)
					local 名称 = ""
					if 奖励参数<=140 then
						名称="5W两银子"
						玩家数据[cyid].角色:添加银子(50000,"帮派迷宫",1)
						常规提示(cyid,"#Y/你获得了#G"..名称)
					elseif 奖励参数<=200 then
						名称="金银锦盒"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					elseif 奖励参数<=235 then
						名称="金柳露"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接,"成功")
					elseif 奖励参数<=250 then
						名称="超级金柳露"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					elseif 奖励参数<=265 then
						名称="召唤兽内丹"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接,"成功")
					elseif 奖励参数<=270 then
						名称="高级召唤兽内丹"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接,"成功")
					elseif 奖励参数<=290 then
						名称="魔兽要诀"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接,"成功")
					elseif 奖励参数<=295 then
						名称="高级魔兽要诀"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接,"成功")
					elseif 奖励参数<=310 then
						名称="五宝盒"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					elseif 奖励参数<=315 then
						名称="神兜兜"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					elseif 奖励参数<=360 then
						名称=取强化石()
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					elseif 奖励参数<=365 then
						名称="特效宝珠"
						if 取随机数()<15 then
							名称="陨铁"
						end
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接,"成功")
					elseif 奖励参数<=380 then
						名称=取宝石()
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					elseif 奖励参数<=390 then
						名称="修炼果"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					elseif 奖励参数<=400 then
						名称="九转金丹"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,取随机数(100,200),nil,链接,"成功")
					elseif 奖励参数<=440 then
						名称="彩果"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					else
						名称="如意丹"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					end
				end
			end
		elseif 物品=="银钥匙" then
			for n=1,#队伍数据[队伍id].成员数据 do
				local cyid = 队伍数据[队伍id].成员数据[n]
				if 玩家数据[cyid].道具:消耗背包道具(玩家数据[cyid].连接id,cyid,"银钥匙",1) then
					链接={提示=format("#S(帮派迷宫)#R/%s#Y开启银宝箱时获得了",玩家数据[cyid].角色.名称),频道="hd",结尾="#Y。"}
					local 奖励参数=取随机数(1,460)
					local 名称 = ""
					if 奖励参数<=160 then
						名称="5W两银子"
						玩家数据[cyid].角色:添加银子(50000,"帮派迷宫",1)
						常规提示(cyid,"#Y/你获得了#G"..名称)
					elseif 奖励参数<=220 then
						名称="金银锦盒"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					elseif 奖励参数<=235 then
						名称="金柳露"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接,"成功")
					elseif 奖励参数<=240 then
						名称="超级金柳露"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					elseif 奖励参数<=250 then
						名称="召唤兽内丹"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接,"成功")
					elseif 奖励参数<=255 then
						名称="高级召唤兽内丹"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接,"成功")
					elseif 奖励参数<=280 then
						名称="魔兽要诀"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接,"成功")
					elseif 奖励参数<=285 then
						名称="高级魔兽要诀"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接,"成功")
					elseif 奖励参数<=300 then
						名称="五宝盒"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					elseif 奖励参数<=310 then
						名称="神兜兜"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					elseif 奖励参数<=360 then
						名称=取强化石()
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					elseif 奖励参数<=363 then
						名称="特效宝珠"
						if 取随机数()<15 then
							名称="陨铁"
						end
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接,"成功")
					elseif 奖励参数<=380 then
						名称=取宝石()
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					elseif 奖励参数<=390 then
						名称="修炼果"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					elseif 奖励参数<=400 then
						名称="九转金丹"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,取随机数(100,200),nil,链接,"成功")
					elseif 奖励参数<=440 then
						名称="彩果"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					else
						名称="如意丹"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					end
				end
			end
		elseif 物品=="铜钥匙" then
			for n=1,#队伍数据[队伍id].成员数据 do
				local cyid = 队伍数据[队伍id].成员数据[n]
				if 玩家数据[cyid].道具:消耗背包道具(玩家数据[cyid].连接id,cyid,"铜钥匙",1) then
					链接={提示=format("#S(帮派迷宫)#R/%s#Y开启铜宝箱时获得了",玩家数据[cyid].角色.名称),频道="hd",结尾="#Y。"}
					local 奖励参数=取随机数(1,310)
					local 名称 = ""
					if 奖励参数<=190 then
						名称="5W两银子"
						玩家数据[cyid].角色:添加银子(50000,"帮派迷宫",1)
						常规提示(cyid,"#Y/你获得了#G"..名称)
					elseif 奖励参数<=230 then
						名称="金银锦盒"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					elseif 奖励参数<=245 then
						名称="金柳露"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接,"成功")
					elseif 奖励参数<=250 then
						名称="超级金柳露"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					elseif 奖励参数<=255 then
						名称="召唤兽内丹"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接,"成功")
					elseif 奖励参数<=256 then
						名称="高级召唤兽内丹"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接,"成功")
					elseif 奖励参数<=265 then
						名称="魔兽要诀"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接,"成功")
					elseif 奖励参数<=261 then
						名称="高级魔兽要诀"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接,"成功")
					elseif 奖励参数<=265 then
						名称="五宝盒"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					elseif 奖励参数<=266 then
						名称="神兜兜"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					elseif 奖励参数<=290 then
						名称=取强化石()
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					elseif 奖励参数<=300 then
						名称="彩果"
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					else
						名称=取宝石()
						玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接,"成功")
					end
				end
			end
		end
	else
		地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].单位编号)
		任务数据[任务id]=nil
		if 物品=="金钥匙" then
			if 玩家数据[id].道具:消耗背包道具(玩家数据[id].连接id,id,"金钥匙",1) then
				链接={提示=format("#S(帮派迷宫)#R/%s#Y开启金宝箱时获得了",玩家数据[id].角色.名称),频道="hd",结尾="#Y。"}
				local 奖励参数=取随机数(1,460)
				local 名称 = ""
				if 奖励参数<=140 then
					名称="5W两银子"
					玩家数据[id].角色:添加银子(50000,"帮派迷宫",1)
					常规提示(id,"#Y/你获得了#G"..名称)
				elseif 奖励参数<=200 then
					名称="金银锦盒"
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				elseif 奖励参数<=235 then
					名称="金柳露"
					玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
				elseif 奖励参数<=250 then
					名称="超级金柳露"
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				elseif 奖励参数<=265 then
					名称="召唤兽内丹"
					玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
				elseif 奖励参数<=270 then
					名称="高级召唤兽内丹"
					玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
				elseif 奖励参数<=290 then
					名称="魔兽要诀"
					玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
				elseif 奖励参数<=295 then
					名称="高级魔兽要诀"
					玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
				elseif 奖励参数<=310 then
					名称="五宝盒"
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				elseif 奖励参数<=315 then
					名称="神兜兜"
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				elseif 奖励参数<=360 then
					名称=取强化石()
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				elseif 奖励参数<=365 then
					名称="特效宝珠"
					if 取随机数()<15 then
						名称="陨铁"
					end
					玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
				elseif 奖励参数<=380 then
					名称=取宝石()
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				elseif 奖励参数<=390 then
					名称="修炼果"
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				elseif 奖励参数<=400 then
					名称="九转金丹"
					玩家数据[id].道具:给予超链接道具(id,名称,取随机数(100,200),nil,链接,"成功")
				elseif 奖励参数<=440 then
					名称="彩果"
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				else
					名称="如意丹"
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				end
			end
		elseif 物品=="银钥匙" then
			if 玩家数据[id].道具:消耗背包道具(玩家数据[id].连接id,id,"银钥匙",1) then
				链接={提示=format("#S(帮派迷宫)#R/%s#Y开启银宝箱时获得了",玩家数据[id].角色.名称),频道="hd",结尾="#Y。"}
				local 奖励参数=取随机数(1,460)
				local 名称 = ""
				if 奖励参数<=160 then
					名称="5W两银子"
					玩家数据[id].角色:添加银子(50000,"帮派迷宫",1)
					常规提示(id,"#Y/你获得了#G"..名称)
				elseif 奖励参数<=220 then
					名称="金银锦盒"
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				elseif 奖励参数<=235 then
					名称="金柳露"
					玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
				elseif 奖励参数<=240 then
					名称="超级金柳露"
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				elseif 奖励参数<=250 then
					名称="召唤兽内丹"
					玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
				elseif 奖励参数<=255 then
					名称="高级召唤兽内丹"
					玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
				elseif 奖励参数<=280 then
					名称="魔兽要诀"
					玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
				elseif 奖励参数<=285 then
					名称="高级魔兽要诀"
					玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
				elseif 奖励参数<=300 then
					名称="五宝盒"
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				elseif 奖励参数<=310 then
					名称="神兜兜"
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				elseif 奖励参数<=360 then
					名称=取强化石()
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				elseif 奖励参数<=363 then
					名称="特效宝珠"
					if 取随机数()<15 then
						名称="陨铁"
					end
					玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
				elseif 奖励参数<=380 then
					名称=取宝石()
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				elseif 奖励参数<=390 then
					名称="修炼果"
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				elseif 奖励参数<=400 then
					名称="九转金丹"
					玩家数据[id].道具:给予超链接道具(id,名称,取随机数(100,200),nil,链接,"成功")
				elseif 奖励参数<=440 then
					名称="彩果"
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				else
					名称="如意丹"
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				end
			end
		elseif 物品=="铜钥匙" then
			if 玩家数据[id].道具:消耗背包道具(玩家数据[id].连接id,id,"铜钥匙",1) then
				链接={提示=format("#S(帮派迷宫)#R/%s#Y开启铜宝箱时获得了",玩家数据[id].角色.名称),频道="hd",结尾="#Y。"}
				local 奖励参数=取随机数(1,310)
				local 名称 = ""
				if 奖励参数<=190 then
					名称="5W两银子"
					玩家数据[id].角色:添加银子(50000,"帮派迷宫",1)
					常规提示(id,"#Y/你获得了#G"..名称)
				elseif 奖励参数<=230 then
					名称="金银锦盒"
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				elseif 奖励参数<=245 then
					名称="金柳露"
					玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
				elseif 奖励参数<=250 then
					名称="超级金柳露"
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				elseif 奖励参数<=255 then
					名称="召唤兽内丹"
					玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
				elseif 奖励参数<=256 then
					名称="高级召唤兽内丹"
					玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
				elseif 奖励参数<=265 then
					名称="魔兽要诀"
					玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
				elseif 奖励参数<=261 then
					名称="高级魔兽要诀"
					玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
				elseif 奖励参数<=265 then
					名称="五宝盒"
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				elseif 奖励参数<=266 then
					名称="神兜兜"
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				elseif 奖励参数<=290 then
					名称=取强化石()
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				elseif 奖励参数<=300 then
					名称="彩果"
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				else
					名称=取宝石()
					玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接,"成功")
				end
			end
		end
	end
end
function 帮派迷宫:战斗胜利处理(id组,战斗类型,任务id)
	if 任务数据[任务id]==nil then
		return
	end
	if self.激活成功~=false then
		local id=id组[1]
		self:检查队伍数据(id)
		if 战斗类型==130053 then 
			for n=1,#id组 do
				local cyid=id组[n]
				self.数据[cyid].入侵者次数=self.数据[cyid].入侵者次数+1
				常规提示(cyid,"#Y/你获得了25点信任度，当前信任度为：#G"..(self.数据[cyid].入侵者次数*25))
				添加帮贡(cyid,3)
				local 等级=玩家数据[cyid].角色.等级
				local 经验=等级*取随机数(95,105)
				local 银子=等级*30
				玩家数据[cyid].角色:添加经验(经验,"帮派迷宫",1)
				玩家数据[cyid].角色:添加银子(银子,"帮派迷宫",1)
			end
			地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].单位编号)
			任务数据[任务id]=nil
		elseif 战斗类型==130054 then 
			local 奖励
			local 几率=取随机数(1,40)
			if 几率<=5 then
				奖励="金钥匙"
			elseif 几率<=15 then
				奖励="银钥匙"
			else
				奖励="铜钥匙"
			end
			for n=1,#id组 do
				local cyid=id组[n]
				self.数据[cyid].秽气妖次数=self.数据[cyid].秽气妖次数+1
				if self.数据[cyid].秽气妖次数==10 then
					广播消息({内容=format("#Y【%s】#W击杀了#Y10只#W秽气妖的兄弟跟我前往迷宫破阵，帮派大计，指日可待。",玩家数据[cyid].角色.名称),频道="hd"})
				end
				添加帮贡(cyid,取随机数(3,6))
				local 等级=玩家数据[cyid].角色.等级
				local 经验=等级*取随机数(95,105)
				local 银子=等级*35
				玩家数据[cyid].角色:添加经验(经验,"帮派迷宫",1)
				玩家数据[cyid].角色:添加银子(银子,"帮派迷宫",1)
				if 奖励~=nil then
					玩家数据[cyid].道具:给予道具(cyid,奖励,1,nil,nil,"专用")
				end
			end
			if self.数据[id组[1]].秽气妖次数==10 then
				self:设置任务(id组[1],2)
			end
			地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].单位编号)
			任务数据[任务id]=nil
			if self.活动开关 and 地图处理类:取地图怪物数量(1230,"秽气妖")<15 then
				self:秽气妖()
			end
		elseif 战斗类型==130055 then 
			local 奖励
			local 几率=取随机数()
			if 几率<=50 then
				奖励="金钥匙"
			else
				奖励="银钥匙"
			end
			for n=1,#id组 do
				local cyid=id组[n]
				self.数据[cyid].天才阵眼=1
				添加帮贡(cyid,10)
				local 等级=玩家数据[cyid].角色.等级
				local 经验=等级*取随机数(100,110)
				local 银子=等级*35
				玩家数据[cyid].角色:添加经验(经验,"帮派迷宫",1)
				玩家数据[cyid].角色:添加银子(银子,"帮派迷宫",1)
				if 奖励~=nil then
					玩家数据[cyid].道具:给予道具(cyid,奖励,1,nil,nil,"专用")
				end
			end
			if self.数据[id组[1]].地才阵眼 and self.数据[id组[1]].人才阵眼 and self.数据[id组[1]].天才阵眼 then
				self:设置任务(id组[1],4)
			end
			地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].单位编号)
			任务数据[任务id]=nil
		elseif 战斗类型==130056 then 
			local 奖励
			local 几率=取随机数()
			if 几率<=50 then
				奖励="金钥匙"
			else
				奖励="银钥匙"
			end
			for n=1,#id组 do
				local cyid=id组[n]
				self.数据[cyid].地才阵眼=1
				添加帮贡(cyid,10)
				local 等级=玩家数据[cyid].角色.等级
				local 经验=等级*取随机数(105,115)
				local 银子=等级*40
				玩家数据[cyid].角色:添加经验(经验,"帮派迷宫",1)
				玩家数据[cyid].角色:添加银子(银子,"帮派迷宫",1)
				if 奖励~=nil then
					玩家数据[cyid].道具:给予道具(cyid,奖励,1,nil,nil,"专用")
				end
			end
			if self.数据[id组[1]].地才阵眼 and self.数据[id组[1]].人才阵眼 and self.数据[id组[1]].天才阵眼 then
				self:设置任务(id组[1],4)
			end
			地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].单位编号)
			任务数据[任务id]=nil
		elseif 战斗类型==130057 then 
			local 奖励
			local 几率=取随机数()
			if 几率<=50 then
				奖励="金钥匙"
			else
				奖励="银钥匙"
			end
			for n=1,#id组 do
				local cyid=id组[n]
				self.数据[cyid].人才阵眼=1
				添加帮贡(cyid,10)
				local 等级=玩家数据[cyid].角色.等级
				local 经验=等级*取随机数(110,120)
				local 银子=等级*45
				玩家数据[cyid].角色:添加经验(经验,"帮派迷宫",1)
				玩家数据[cyid].角色:添加银子(银子,"帮派迷宫",1)
				if 奖励~=nil then
					玩家数据[cyid].道具:给予道具(cyid,奖励,1,nil,nil,"专用")
				end
			end
			if self.数据[id组[1]].地才阵眼 and self.数据[id组[1]].人才阵眼 and self.数据[id组[1]].天才阵眼 then
				self:设置任务(id组[1],4)
			end
			地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].单位编号)
			任务数据[任务id]=nil
		elseif 战斗类型==130058 then 
			for n=1,#id组 do
				local cyid=id组[n]
				self.数据[cyid].天才=1
				添加帮贡(cyid,20)
				local 等级=玩家数据[cyid].角色.等级
				local 经验=等级*取随机数(115,125)
				local 银子=等级*50
				玩家数据[cyid].角色:添加经验(经验,"帮派迷宫",1)
				玩家数据[cyid].角色:添加银子(银子,"帮派迷宫",1)
				玩家数据[cyid].道具:给予道具(cyid,"金钥匙",1,nil,nil,"专用")
			end
			if self.数据[id组[1]].人才 and self.数据[id组[1]].地才 and self.数据[id组[1]].天才 then
				self:设置任务(id组[1],6)
			end
		elseif 战斗类型==130059 then 
			for n=1,#id组 do
				local cyid=id组[n]
				self.数据[cyid].地才=1
				添加帮贡(cyid,20)
				local 等级=玩家数据[cyid].角色.等级
				local 经验=等级*取随机数(120,130)
				local 银子=等级*55
				玩家数据[cyid].角色:添加经验(经验,"帮派迷宫",1)
				玩家数据[cyid].角色:添加银子(银子,"帮派迷宫",1)
				玩家数据[cyid].道具:给予道具(cyid,"金钥匙",1,nil,nil,"专用")
			end
			if self.数据[id组[1]].人才 and self.数据[id组[1]].地才 and self.数据[id组[1]].天才 then
				self:设置任务(id组[1],6)
			end
		elseif 战斗类型==130060 then 
			for n=1,#id组 do
				local cyid=id组[n]
				self.数据[cyid].人才=1
				添加帮贡(cyid,20)
				local 等级=玩家数据[cyid].角色.等级
				local 经验=等级*取随机数(125,135)
				local 银子=等级*60
				玩家数据[cyid].角色:添加经验(经验,"帮派迷宫",1)
				玩家数据[cyid].角色:添加银子(银子,"帮派迷宫",1)
				玩家数据[cyid].道具:给予道具(cyid,"金钥匙",1,nil,nil,"专用")
			end
			if self.数据[id组[1]].人才 and self.数据[id组[1]].地才 and self.数据[id组[1]].天才 then
				self:设置任务(id组[1],6)
			end
		elseif 战斗类型==130061 then 
			for n=1,#id组 do
				local cyid=id组[n]
				self.数据[cyid].劳释道人=1
				添加帮贡(cyid,30)
				local 等级=玩家数据[cyid].角色.等级
				local 经验=等级*取随机数(125,135)
				local 银子=等级*60
				玩家数据[cyid].角色:添加经验(经验,"帮派迷宫",1)
				玩家数据[cyid].角色:添加银子(银子,"帮派迷宫",1)
				玩家数据[cyid].道具:给予道具(cyid,"金钥匙",1,nil,nil,"专用")
			end
			self:设置任务(id组[1],7)
		elseif 战斗类型==130062 then 
			local 奖励
			local 几率=取随机数(1,100)
			if 几率<=5 then
				奖励="金钥匙"
			elseif 几率<=15 then
				奖励="银钥匙"
			elseif 几率<=35 then
				奖励="铜钥匙"
			end
			for n=1,#id组 do
				local cyid=id组[n]
				添加帮贡(cyid,取随机数(8,13))
				local 等级=玩家数据[cyid].角色.等级
				local 经验=等级*取随机数(120,130)
				local 银子=等级*55
				玩家数据[cyid].角色:添加经验(经验,"帮派迷宫",1)
				玩家数据[cyid].角色:添加银子(银子,"帮派迷宫",1)
				if 奖励~=nil then
					玩家数据[cyid].道具:给予道具(cyid,奖励,1,nil,nil,"专用")
				end
			end
			地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].单位编号)
			任务数据[任务id]=nil
			if self.活动开关 and 地图处理类:取地图怪物数量(2002,"秽气源头")<15 then
				self:秽气源头()
			end
		elseif 战斗类型==130063 then 
			for n=1,#id组 do
				local cyid=id组[n]
				添加帮贡(cyid,100)
				local 等级=玩家数据[cyid].角色.等级
				local 经验=等级*取随机数(95,105)
				local 银子=等级*40
				玩家数据[cyid].角色:添加经验(经验,"帮派迷宫",1)
				玩家数据[cyid].角色:添加银子(银子,"帮派迷宫",1)
				玩家数据[cyid].道具:给予道具(cyid,"金钥匙",2,nil,nil,"专用")
			end
			广播消息({内容=format("#P【迷宫之主】#W带领队伍成功挑战了#G迷宫金龙#W，因此#G灵木宝树#W为其诞下一个专属祈愿宝箱。#17"),频道="hd"})
			self:祈愿宝箱(self.激活成功.开启id)
			地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].单位编号)
			任务数据[任务id]=nil
		end
	else
		地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].单位编号)
单
	end
end
function 帮派迷宫:加载公共数据()
	地图处理类.地图数据[2002]={npc={},单位={},传送圈={}}
	地图处理类.地图玩家[2002]={}
	地图处理类.地图坐标[2002]=地图处理类.地图坐标[1214]
	地图处理类.地图单位[2002]={}
	地图处理类.单位编号[2002]=1000
	地图处理类.地图数据[2003]={npc={},单位={},传送圈={}}
	地图处理类.地图玩家[2003]={}
	地图处理类.地图坐标[2003]=地图处理类.地图坐标[1212]
	地图处理类.地图单位[2003]={}
	地图处理类.单位编号[2003]=1000
	self.宝箱坐标={
	[1]={["x"]=23,["y"]=25},
	[2]={["x"]=25,["y"]=24},
	[3]={["x"]=27,["y"]=23},
	[4]={["x"]=29,["y"]=22},
	[5]={["x"]=31,["y"]=21},
	[6]={["x"]=33,["y"]=20},
	[7]={["x"]=35,["y"]=19},
	[8]={["x"]=37,["y"]=18},
	[9]={["x"]=39,["y"]=17},
	[10]={["x"]=41,["y"]=16},
	[11]={["x"]=43,["y"]=15},
	[12]={["x"]=45,["y"]=14},
	[13]={["x"]=23+2,["y"]=25+1},
	[14]={["x"]=25+2,["y"]=24+1},
	[15]={["x"]=27+2,["y"]=23+1},
	[16]={["x"]=29+2,["y"]=22+1},
	[17]={["x"]=31+2,["y"]=21+1},
	[18]={["x"]=33+2,["y"]=20+1},
	[19]={["x"]=35+2,["y"]=19+1},
	[20]={["x"]=37+2,["y"]=18+1},
	[21]={["x"]=39+2,["y"]=17+1},
	[22]={["x"]=41+2,["y"]=16+1},
	[23]={["x"]=43+2,["y"]=15+1},
	[24]={["x"]=45+2,["y"]=14+1},
	[25]={["x"]=23+4,["y"]=25+2},
	[26]={["x"]=25+4,["y"]=24+2},
	[27]={["x"]=27+4,["y"]=23+2},
	[28]={["x"]=29+4,["y"]=22+2},
	[29]={["x"]=31+4,["y"]=21+2},
	[30]={["x"]=33+4,["y"]=20+2},
	[31]={["x"]=35+4,["y"]=19+2},
	[32]={["x"]=37+4,["y"]=18+2},
	[33]={["x"]=39+4,["y"]=17+2},
	[34]={["x"]=41+4,["y"]=16+2},
	[35]={["x"]=43+4,["y"]=15+2},
	[36]={["x"]=45+4,["y"]=14+2},
	[37]={["x"]=23+6,["y"]=25+3},
	[38]={["x"]=25+6,["y"]=24+3},
	[39]={["x"]=27+6,["y"]=23+3},
	[40]={["x"]=29+6,["y"]=22+3},
	[41]={["x"]=31+6,["y"]=21+3},
	[42]={["x"]=33+6,["y"]=20+3},
	[43]={["x"]=35+6,["y"]=19+3},
	[44]={["x"]=37+6,["y"]=18+3},
	[45]={["x"]=39+6,["y"]=17+3},
	[46]={["x"]=41+6,["y"]=16+3},
	[47]={["x"]=43+6,["y"]=15+3},
	[48]={["x"]=45+6,["y"]=14+3},
	[49]={["x"]=23+8,["y"]=25+4},
	[50]={["x"]=25+8,["y"]=24+4},
	[51]={["x"]=27+8,["y"]=23+4},
	[52]={["x"]=29+8,["y"]=22+4},
	[53]={["x"]=31+8,["y"]=21+4},
	[54]={["x"]=33+8,["y"]=20+4},
	[55]={["x"]=35+8,["y"]=19+4},
	[56]={["x"]=37+8,["y"]=18+4},
	[57]={["x"]=39+8,["y"]=17+4},
	[58]={["x"]=41+8,["y"]=16+4},
	[59]={["x"]=43+8,["y"]=15+4},
	[60]={["x"]=45+8,["y"]=14+4},
	[61]={["x"]=23+10,["y"]=25+5},
	[62]={["x"]=25+10,["y"]=24+5},
	[63]={["x"]=27+10,["y"]=23+5},
	[64]={["x"]=29+10,["y"]=22+5},
	[65]={["x"]=31+10,["y"]=21+5},
	[66]={["x"]=33+10,["y"]=20+5},
	[67]={["x"]=35+10,["y"]=19+5},
	[68]={["x"]=37+10,["y"]=18+5},
	[69]={["x"]=39+10,["y"]=17+5},
	[70]={["x"]=41+10,["y"]=16+5},
	[71]={["x"]=43+10,["y"]=15+5},
	[72]={["x"]=45+10,["y"]=14+5},
	[73]={["x"]=23+12,["y"]=25+6},
	[74]={["x"]=25+12,["y"]=24+6},
	[75]={["x"]=27+12,["y"]=23+6},
	[76]={["x"]=29+12,["y"]=22+6},
	[77]={["x"]=31+12,["y"]=21+6},
	[78]={["x"]=33+12,["y"]=20+6},
	[79]={["x"]=35+12,["y"]=19+6},
	[80]={["x"]=37+12,["y"]=18+6},
	[81]={["x"]=39+12,["y"]=17+6},
	[82]={["x"]=41+12,["y"]=16+6},
	[83]={["x"]=43+12,["y"]=15+6},
	[84]={["x"]=45+12,["y"]=14+6},
	[85]={["x"]=23+14,["y"]=25+7},
	[86]={["x"]=25+14,["y"]=24+7},
	[87]={["x"]=27+14,["y"]=23+7},
	[88]={["x"]=29+14,["y"]=22+7},
	[89]={["x"]=31+14,["y"]=21+7},
	[90]={["x"]=33+14,["y"]=20+7},
	[91]={["x"]=35+14,["y"]=19+7},
	[92]={["x"]=37+14,["y"]=18+7},
	[93]={["x"]=39+14,["y"]=17+7},
	[94]={["x"]=41+14,["y"]=16+7},
	[95]={["x"]=43+14,["y"]=15+7},
	[96]={["x"]=45+14,["y"]=14+7},
	[97]={["x"]=23+16,["y"]=25+8},
	[98]={["x"]=25+16,["y"]=24+8},
	[99]={["x"]=27+16,["y"]=23+8},
	[100]={["x"]=29+16,["y"]=22+8},
	[101]={["x"]=31+16,["y"]=21+8},
	[102]={["x"]=33+16,["y"]=20+8},
	[103]={["x"]=35+16,["y"]=19+8},
	[104]={["x"]=37+16,["y"]=18+8},
	[105]={["x"]=39+16,["y"]=17+8},
	[106]={["x"]=41+16,["y"]=16+8},
	[107]={["x"]=43+16,["y"]=15+8},
	[108]={["x"]=45+16,["y"]=14+8},
	[109]={["x"]=23+18,["y"]=25+9},
	[110]={["x"]=25+18,["y"]=24+9},
	[111]={["x"]=27+18,["y"]=23+9},
	[112]={["x"]=29+18,["y"]=22+9},
	[113]={["x"]=31+18,["y"]=21+9},
	[114]={["x"]=33+18,["y"]=20+9},
	[115]={["x"]=35+18,["y"]=19+9},
	[116]={["x"]=37+18,["y"]=18+9},
	[117]={["x"]=39+18,["y"]=17+9},
	[118]={["x"]=41+18,["y"]=16+9},
	[119]={["x"]=43+18,["y"]=15+9},
	[120]={["x"]=45+18,["y"]=14+9},
	[121]={["x"]=23+20,["y"]=25+10},
	[122]={["x"]=25+20,["y"]=24+10},
	[123]={["x"]=27+20,["y"]=23+10},
	[124]={["x"]=29+20,["y"]=22+10},
	[125]={["x"]=31+20,["y"]=21+10},
	[126]={["x"]=33+20,["y"]=20+10},
	[127]={["x"]=35+20,["y"]=19+10},
	[128]={["x"]=37+20,["y"]=18+10},
	[129]={["x"]=39+20,["y"]=17+10},
	[130]={["x"]=41+20,["y"]=16+10},
	[131]={["x"]=43+20,["y"]=15+10},
	[132]={["x"]=45+20,["y"]=14+10},
	[133]={["x"]=23+22,["y"]=25+11},
	[134]={["x"]=25+22,["y"]=24+11},
	[135]={["x"]=27+22,["y"]=23+11},
	[136]={["x"]=29+22,["y"]=22+11},
	[137]={["x"]=31+22,["y"]=21+11},
	[138]={["x"]=33+22,["y"]=20+11},
	[139]={["x"]=35+22,["y"]=19+11},
	[140]={["x"]=37+22,["y"]=18+11},
	[141]={["x"]=39+22,["y"]=17+11},
	[142]={["x"]=41+22,["y"]=16+11},
	[143]={["x"]=43+22,["y"]=15+11},
	[144]={["x"]=45+22,["y"]=14+11},
	}
end
function 怪物属性:入侵者(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	local sx = self:取属性(等级)
	战斗单位[1]={
	名称="入侵者"
	,模型="山贼"
	,等级=等级
	,气血 = math.floor(sx.属性.气血*6)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,主动技能=sx.技能组
	}
	for i=2,10 do
		sx = self:取属性(等级)
		战斗单位[i]={
		名称="打手"
		,模型="强盗"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*3)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 怪物属性:秽气妖(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	local sx = self:取属性(等级)
	战斗单位[1]={
	名称="秽气妖"
	,模型="蜃气妖"
	,等级=等级
	,气血 = math.floor(sx.属性.气血*6)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,主动技能=sx.技能组
	}
	for i=2,3 do
		sx = self:取属性(等级)
		战斗单位[i]={
		名称="秽气师"
		,模型="蜃气妖"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*4)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,主动技能=sx.技能组
		}
	end
	for i=4,6 do
		sx = self:取属性(等级)
		战斗单位[i]={
		名称="秽气"
		,模型="吸血鬼"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*4)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,主动技能=sx.技能组
		}
	end
	sx = self:取属性(等级,"神木林","法系")
	for i=7,8 do
		战斗单位[i]={
		名称="秽气"
		,模型="吸血鬼"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*4)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,门派="神木林"
		,主动技能=Q_门派法术["神木林"]
		}
	end
	return 战斗单位
end
function 怪物属性:劳释道人(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	local sx = self:取属性(等级)
	战斗单位[1]={
	名称="劳释道人"
	,模型="进阶雨师"
	,等级=等级
	,气血 = math.floor(sx.属性.气血*12)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,主动技能=sx.技能组
	}
	for i=2,3 do
		sx = self:取属性(等级,"龙宫","法系")
		战斗单位[i]={
		名称="至阴"
		,模型="进阶灵符女娲"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*8)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,门派="龙宫"
		,主动技能=Q_门派法术["龙宫"]
		}
	end
	for i=4,5 do
		sx = self:取属性(等级,"神木林","法系")
		战斗单位[i]={
		名称="至阴"
		,模型="进阶灵符女娲"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*8)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,门派="神木林"
		,主动技能=Q_门派法术["神木林"]
		}
	end
	sx = self:取属性(等级,"化生寺","辅助")
	战斗单位[6]={
	名称="阵石"
	,模型="机关人人形"
	,等级=等级
	,气血 = math.floor(sx.属性.气血*8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,门派="化生寺"
	,主动技能=Q_门派法术["化生寺"]
	}
	for i=7,10 do
		sx = self:取属性(等级,"凌波城","物理")
		战斗单位[i]={
		名称="纯阳"
		,模型="进阶天兵"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*8)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,门派="凌波城"
		,主动技能=Q_门派法术["凌波城"]
		}
	end
	return 战斗单位
end
function 怪物属性:秽气源头(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	local sx = self:取属性(等级)
	战斗单位[1]={
	名称="秽气源头"
	,模型="进阶蜃气妖"
	,等级=等级
	,气血 = math.floor(sx.属性.气血*7)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,主动技能=sx.技能组
	}
	for i=2,4 do
		sx = self:取属性(等级)
		战斗单位[i]={
		名称="炎炎浊气"
		,模型="蜃气妖"
		,等级=等级
		,变异=true
		,气血 = math.floor(sx.属性.气血*5)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,主动技能=sx.技能组
		}
	end
	sx = self:取属性(等级,"凌波城","物理")
	战斗单位[5]={
	名称="炎炎浊气"
	,模型="蜃气妖"
	,等级=等级
	,气血 = math.floor(sx.属性.气血*5)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,门派="凌波城"
	,主动技能=Q_门派法术["凌波城"]
	}
	for i=6,8 do
		战斗单位[i]={
		名称="死气沉沉"
		,模型="幽灵"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*5)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,门派="凌波城"
		,主动技能=Q_门派法术["凌波城"]
		}
	end
	return 战斗单位
end
function 怪物属性:人才(任务id,玩家id,序号)
	local wq1={武器="浩气长舒",级别=150} 
	local wq2={武器="霜冷九州",级别=150}
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	local sx = self:取属性(等级,"凌波城","物理")
	战斗单位[1]={
	名称="人才"
	,模型="修罗傀儡鬼"
	,等级=等级
	,变异=true
	,气血 = math.floor(sx.属性.气血*8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,门派="凌波城"
	,主动技能=Q_门派法术["凌波城"]
	}
	sx = self:取属性(等级)
	战斗单位[2]={
	名称="人心莫测"
	,模型="逍遥生"
	,角色=true
	,染色方案=1
	,染色组={[1]=9,[2]=0,[3]=2,序号=3710}
	,武器=取武器数据(wq1.武器,wq1.级别)
	,等级=等级
	,气血 = math.floor(sx.属性.气血*5)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,主动技能=sx.技能组
	}
	sx = self:取属性(等级)
	战斗单位[3]={
	名称="人情义士"
	,模型="剑侠客"
	,角色=true
	,染色方案=2
	,染色组={[1]=3,[2]=0,[3]=2,序号=3710}
	,武器=取武器数据(wq2.武器,wq2.级别)
	,等级=等级
	,气血 = math.floor(sx.属性.气血*5)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,主动技能=sx.技能组
	}
	for i=4,5 do
		sx = self:取属性(等级)
		战斗单位[i]={
		名称="阵石"
		,模型="曼珠沙华"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*5)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,主动技能=sx.技能组
		}
	end
	sx = self:取属性(等级)
	战斗单位[6]={
	名称="劳释虚象"
	,模型="进阶雨师"
	,等级=等级
	,气血 = math.floor(sx.属性.气血*5)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,主动技能=sx.技能组
	}
	for i=7,10 do
		sx = self:取属性(等级)
		战斗单位[i]={
		名称="金石为开"
		,模型="狂豹人形"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*5)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 怪物属性:地才(任务id,玩家id,序号)
	local wq1={武器="牧云清歌",级别=150} 
	local wq2={武器="狂澜碎岳",级别=150}
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	local sx = self:取属性(等级)
	战斗单位[1]={
	名称="地才"
	,模型="机关人人形"
	,等级=等级
	,气血 = math.floor(sx.属性.气血*7)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,主动技能=sx.技能组
	}
	sx = self:取属性(等级,"魔王寨","法系")
	战斗单位[2]={
	名称="地裂"
	,模型="狐美人"
	,角色=true
	,染色方案=7
	,染色组={[1]=2,[2]=0,[3]=2,序号=3710}
	,武器=取武器数据(wq1.武器,wq1.级别)
	,等级=等级
	,气血 = math.floor(sx.属性.气血*5)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,门派="魔王寨"
	,主动技能=Q_门派法术["魔王寨"]
	}
	sx = self:取属性(等级,"狮驼岭","物理")
	战斗单位[3]={
	名称="山崩"
	,模型="虎头怪"
	,角色=true
	,染色方案=6
	,染色组={[1]=2,[2]=0,[3]=2,序号=3710}
	,武器=取武器数据(wq2.武器,wq2.级别)
	,等级=等级
	,气血 = math.floor(sx.属性.气血*5)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,门派="狮驼岭"
	,主动技能=Q_门派法术["狮驼岭"]
	}
	for i=4,5 do
		sx = self:取属性(等级)
		战斗单位[i]={
		名称="阵石"
		,模型="猫灵兽形"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*5)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,主动技能=sx.技能组
		}
	end
	sx = self:取属性(等级)
	战斗单位[6]={
	名称="劳释虚象"
	,模型="进阶雨师"
	,等级=等级
	,气血 = math.floor(sx.属性.气血*5)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,主动技能=sx.技能组
	}
	for i=7,10 do
		sx = self:取属性(等级)
		战斗单位[i]={
		名称="生死轮回"
		,模型="鬼将"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*5)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 怪物属性:天才(任务id,玩家id,序号)
	local wq1={武器="丝萝乔木",级别=150} 
	local wq2={武器="天龙破城",级别=150}
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	local sx = self:取属性(等级,"凌波城","物理")
	战斗单位[1]={
	名称="天才"
	,模型="阴阳伞"
	,等级=等级
	,变异=true
	,气血 = math.floor(sx.属性.气血*7)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,门派="凌波城"
	,主动技能=Q_门派法术["凌波城"]
	}
	sx = self:取属性(等级,"龙宫","法系")
	战斗单位[2]={
	名称="天佑我成"
	,模型="玄彩娥"
	,角色=true
	,染色方案=1
	,染色组={[1]=9,[2]=0,[3]=2,序号=3710}
	,武器=取武器数据(wq1.武器,wq1.级别)
	,等级=等级
	,气血 = math.floor(sx.属性.气血*5)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,门派="龙宫"
	,主动技能=Q_门派法术["龙宫"]
	}
	sx = self:取属性(等级,"天宫","法系")
	战斗单位[3]={
	名称="天网恢恢"
	,模型="龙太子"
	,角色=true
	,染色方案=6
	,染色组={[1]=2,[2]=0,[3]=2,序号=3710}
	,武器=取武器数据(wq2.武器,wq2.级别)
	,等级=等级
	,气血 = math.floor(sx.属性.气血*5)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,门派="天宫"
	,主动技能=Q_门派法术["天宫"]
	}
	for i=4,5 do
		sx = self:取属性(等级)
		战斗单位[i]={
		名称="阵石"
		,模型="混沌兽"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*5)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,主动技能=sx.技能组
		}
	end
	sx = self:取属性(等级)
	战斗单位[6]={
	名称="劳释虚象"
	,模型="进阶雨师"
	,等级=等级
	,气血 = math.floor(sx.属性.气血*5)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,主动技能=sx.技能组
	}
	for i=7,10 do
		sx = self:取属性(等级)
		战斗单位[i]={
		名称="善恶有报"
		,模型="律法女娲"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*5)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 怪物属性:人才阵眼(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	local sx = self:取属性(等级)
	战斗单位[1]={
	名称="阵眼"
	,模型="进阶机关人人形"
	,等级=等级
	,气血 = math.floor(sx.属性.气血*7)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,主动技能=sx.技能组
	}
	for i=2,3 do
		sx = self:取属性(等级,"神木林","法系")
		战斗单位[i]={
		名称="阵型"
		,模型="巴蛇"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*5)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,门派="神木林"
		,主动技能=Q_门派法术["神木林"]
		}
	end
	for i=4,5 do
		sx = self:取属性(等级)
		战斗单位[i]={
		名称="阵型"
		,模型="巴蛇"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*5)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,主动技能=sx.技能组
		}
	end
	for i=6,8 do
		sx = self:取属性(等级)
		战斗单位[i]={
		名称="阵石"
		,模型="进阶机关兽"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*5)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 怪物属性:地才阵眼(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	local sx = self:取属性(等级)
	战斗单位[1]={
	名称="阵眼"
	,模型="进阶机关人人形"
	,等级=等级
	,气血 = math.floor(sx.属性.气血*7)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,主动技能=sx.技能组
	}
	for i=2,3 do
		sx = self:取属性(等级,"神木林","法系")
		战斗单位[i]={
		名称="阵型"
		,模型="巴蛇"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*5)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,门派="神木林"
		,主动技能=Q_门派法术["神木林"]
		}
	end
	for i=4,5 do
		sx = self:取属性(等级)
		战斗单位[i]={
		名称="阵型"
		,模型="巴蛇"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*5)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,主动技能=sx.技能组
		}
	end
	for i=6,8 do
		sx = self:取属性(等级)
		战斗单位[i]={
		名称="阵石"
		,模型="进阶机关兽"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*5)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 怪物属性:天才阵眼(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	local sx = self:取属性(等级)
	战斗单位[1]={
	名称="阵眼"
	,模型="进阶机关人人形"
	,等级=等级
	,气血 = math.floor(sx.属性.气血*7)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,主动技能=sx.技能组
	}
	for i=2,3 do
		sx = self:取属性(等级,"神木林","法系")
		战斗单位[i]={
		名称="阵型"
		,模型="巴蛇"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*5)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,门派="神木林"
		,主动技能=Q_门派法术["神木林"]
		}
	end
	for i=4,5 do
		sx = self:取属性(等级)
		战斗单位[i]={
		名称="阵型"
		,模型="巴蛇"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*5)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,主动技能=sx.技能组
		}
	end
	for i=6,8 do
		sx = self:取属性(等级)
		战斗单位[i]={
		名称="阵石"
		,模型="进阶机关兽"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*5)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 怪物属性:金龙(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	local sx = self:取属性(等级,"凌波城","物理")
	战斗单位[1]={
	名称="金龙"
	,模型="蛟龙"
	,等级=等级
	,变异 = true
	,附加阵法="虎翼阵"
	,气血 = math.floor(sx.属性.气血*12)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,技能={"感知"}
	,抵抗封印等级=100
	,门派="凌波城"
	,主动技能=Q_门派法术["凌波城"]
	}
	sx = self:取属性(等级,"神木林","法系")
	战斗单位[2]={
	名称="玉龙"
	,模型="蛟龙"
	,等级=等级
	,气血 = math.floor(sx.属性.气血*8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,技能={"感知"}
	,抵抗封印等级=100
	,门派="神木林"
	,主动技能=Q_门派法术["神木林"]
	}
	sx = self:取属性(等级,"神木林","法系")
	战斗单位[3]={
	名称="银龙"
	,模型="蛟龙"
	,等级=等级
	,气血 = math.floor(sx.属性.气血*5)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,技能={"感知"}
	,抵抗封印等级=100
	,门派="神木林"
	,主动技能=Q_门派法术["神木林"]
	}
	for i=4,5 do
		sx = self:取属性(等级)
		战斗单位[i]={
		名称="龙女"
		,模型="小龙女"
		,变异 = true
		,等级=等级
		,气血 = math.floor(sx.属性.气血*5)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,技能={"感知"}
		,主动技能=sx.技能组
		}
	end
	sx = self:取属性(等级)
	战斗单位[6]={
	名称="蝶"
	,模型="进阶蝴蝶仙子"
	,等级=等级
	,气血 = math.floor(sx.属性.气血*5)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,技能={"感知"}
	,主动技能=sx.技能组
	}
	for i=7,8 do
		sx = self:取属性(等级)
		战斗单位[i]={
		名称="芙"
		,模型="芙蓉仙子"
		,等级=等级
		,气血 = math.floor(sx.属性.气血*5)
		,伤害 = math.floor(sx.属性.伤害)
		,法伤 = math.floor(sx.属性.法伤)
		,速度 = math.floor(sx.属性.速度)
		,固定伤害 = math.floor(等级)
		,治疗能力 = math.floor(等级)
		,技能={"感知"}
		,主动技能=sx.技能组
		}
	end
	sx = self:取属性(等级,"方寸山","封系")
	战斗单位[9]={
	名称="凤"
	,模型="凤凰"
	,变异 =true
	,等级=等级
	,气血 = math.floor(sx.属性.气血*5)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,封印命中等级=100
	,抵抗封印等级=100
	,技能={"感知"}
	,门派="方寸山"
	,主动技能=Q_门派法术["方寸山"]
	}
	sx = self:取属性(等级,"化生寺","辅助")
	战斗单位[10]={
	名称="生"
	,模型="凤凰"
	,变异 =true
	,等级=等级
	,气血 = math.floor(sx.属性.气血*5)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级)
	,治疗能力 = math.floor(等级)
	,封印命中等级=100
	,抵抗封印等级=100
	,技能={"感知"}
	,门派="化生寺"
	,主动技能=Q_门派法术["化生寺"]
	}
	return 战斗单位
end
return 帮派迷宫