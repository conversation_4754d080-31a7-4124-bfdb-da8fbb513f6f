-- @Author: 作者QQ381990860
-- @Date:   2022-11-05 02:15:11
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-06-09 16:43:29
--------------------------------------------------
操作冷却时间 = 操作冷却时间 or {}
助战处理类 = class()
function 助战处理类:数据处理(id, 数据序列, 内容, 参数,编号)
    local UserData=玩家数据
    if 数据序列==1 then
        self:获取数据(玩家数据[id])
    elseif 数据序列==13 then
        self:入队处理(玩家数据[id],参数)
    elseif 数据序列==57 then
        self:更换操作主角(玩家数据[id],tonumber(参数))
    end
end
function 助战处理类:加载助战玩家(user,mateuser)
    系统处理类:助战进入游戏(mateuser.数字id+1000000,user.账号,mateuser.数字id,user.角色.ip)
    mateuser.id=tonumber(mateuser.数字id)
    玩家数据[mateuser.数字id].对接id=user.连接id
    玩家数据[mateuser.数字id].助战识别=1
    地图处理类:跳转地图(mateuser.数字id,user.角色.地图数据.编号,user.角色.地图数据.x/20,user.角色.地图数据.y/20)
    队伍数据[user.id].成员数据[#队伍数据[user.id].成员数据+1]=mateuser.数字id
    玩家数据[mateuser.数字id].队伍=user.id
    for n=1,#队伍数据[user.id].成员数据 do
        队伍处理类:索取队伍信息(队伍数据[user.id].成员数据[n],4004)
    end
    队伍处理类:同步飞行坐骑(user.id,mateuser.数字id)
end
function 助战处理类:入队处理(user, bh)
    local 当前时间 = os.time()
    if 操作冷却时间[user.id] and 当前时间 - 操作冷却时间[user.id] < 1 then
        常规提示(user.id, "#Y/操作过于频繁，请稍后再试")
        -- 发送当前状态回客户端，保持按钮状态一致
        self:刷新指定数据(user, bh, "入队退队")
        return
    end
    操作冷却时间[user.id] = 当前时间
    local mateuser = user.助战 and user.助战[bh]
    if not mateuser then
        常规提示(user.id, "#Y/不存在这个助战")
        return
    end

    -- 记录操作前的状态，用于失败时恢复
    local 原状态 = mateuser.状态
    local 目标状态 = (not 原状态 or 原状态 == "出队" or 原状态 == "退队") and "入队" or "出队"

    -- 先将状态设置为目标状态，但这只是临时的，如果操作失败会恢复
    mateuser.状态 = 目标状态

    local function checkAndOperate()
        if user.队伍 == 0 or user.队伍 ~= user.id then
            常规提示(user.id, "#Y/请先为自己创建一个队伍")
            -- 恢复原状态
            mateuser.状态 = 原状态
            return false
        end
        -- 只在入队操作时检查队伍是否已满
        if 目标状态 == "入队" and #队伍数据[user.队伍].成员数据 >= 5 then
            常规提示(user.id, "#Y/队伍已满无法操作")
            -- 恢复原状态
            mateuser.状态 = 原状态
            return false
        end
        if mateuser.等级 <= 10 then
            常规提示(user.id, "#Y/该角色等级不满足条件")
            -- 恢复原状态
            mateuser.状态 = 原状态
            return false
        end
        if 原状态 == "跑商" then
            常规提示(user.id, "#Y/对方正在跑商，无法入队")
            -- 恢复原状态
            mateuser.状态 = 原状态
            return false
        end
        local 地图编号 = 玩家数据[user.id].角色.地图数据.编号
        if 地图编号 >= 1340 and 地图编号 <= 1342 or 地图编号 == 1332 or 地图编号 >= 1600 and 地图编号 <= 1620 or 地图编号 >= 6000 and 地图编号 <= 7500 or 地图编号==2000 then
            常规提示(user.id, "#Y/副本中禁止该操作")
            -- 恢复原状态
            mateuser.状态 = 原状态
            return false
        end
        if 玩家数据[mateuser.id] and 玩家数据[mateuser.id].摊位数据 ~= nil then
            常规提示(user.id, "#Y/对方正在摆摊，无法入队")
            -- 恢复原状态
            mateuser.状态 = 原状态
            return false
        end
        -- 检查玩家在线并且是非助战状态，禁止利用招聘队伍直接以助战形式顶号登陆
        if 玩家数据[mateuser.id] then
            if not 玩家数据[mateuser.id].助战识别 then
                常规提示(user.id, "#Y/该角色正在线上游戏，无法以助战形式入队")
                -- 恢复原状态
                mateuser.状态 = 原状态
                return false
            end
        end

        -- 操作成功，状态已经设置为目标状态
        if 目标状态 == "入队" then
            self:加载助战玩家(user, mateuser)
        end
        return true
    end

    if 玩家数据[mateuser.id] and 玩家数据[mateuser.id].助战识别 and (mateuser.状态 == "退队" or not mateuser.状态) then
        常规提示(user.id, "#Y/该角色已上线,无法操作")
        -- 恢复原状态并通知客户端
        mateuser.状态 = 原状态
        self:刷新指定数据(user, bh, "入队退队")
    else
        if 目标状态 == "出队" and 原状态 == "入队" then
            mateuser.状态 = "退队"
            -- 确保助战角色存在且有效
            if 玩家数据[mateuser.id] and 玩家数据[mateuser.id].助战识别 then
             --   print("助战处理类:入队处理 - 断开助战角色: ID = " .. mateuser.id)
                系统处理类:断开游戏(mateuser.id)
            else
             --   print("助战处理类:入队处理 - 助战角色不存在或无效: ID = " .. mateuser.id)
                -- 如果助战角色不存在，直接将状态设置为"出队"
                mateuser.状态 = "出队"
            end
            self:刷新指定数据(user, bh, "入队退队")
        elseif checkAndOperate() then
            -- 操作成功，发送状态回客户端
            self:刷新指定数据(user, bh, "入队退队")
        else
            -- 操作失败，发送当前状态回客户端
            self:刷新指定数据(user, bh, "入队退队")
        end
    end
    发送数据(user.连接id, 420008, user.角色.助战信息 or {})
end

function 助战处理类:加载数据(user)
    user.助战={}
end
function 助战处理类:获取数据(user)
    local 信息组=table.loadstring(读入文件("data/"..user.账号.."/信息.txt"))
    if not user.助战 then
        user.助战={}
    end
    for k,v in pairs(信息组) do
        local 允许添加=1
        for k1,v1 in pairs(user.助战) do
            if v1 and v1.id==v  then
                允许添加=nil
            end
        end
        if v~=user.id and #user.助战<=4 and 允许添加 then
            user.助战[#user.助战+1]=table.loadstring(读入文件([[data/]]..user.账号..[[/]]..v..[[/角色.txt]]))
            -- 确保助战数据有正确的id字段
            user.助战[#user.助战].id = v
            user.助战[#user.助战].数字id = v
            local 临时道具=table.loadstring(读入文件([[data/]]..user.账号..[[/]]..v..[[/道具.txt]]))
            user.助战[#user.助战].装备数据={装备={},灵饰={},法宝={},锦衣={},灵宝={}}
            for k,v in pairs(user.助战[#user.助战].装备) do
                user.助战[#user.助战].装备数据.装备[k]=临时道具[v]
            end
            for k,v in pairs(user.助战[#user.助战].灵饰) do
                user.助战[#user.助战].装备数据.灵饰[k]=临时道具[v]
            end
            for k,v in pairs(user.助战[#user.助战].法宝佩戴) do
                user.助战[#user.助战].装备数据.法宝[k]=临时道具[v]
            end
            for k,v in pairs(user.助战[#user.助战].锦衣) do
                if k>=3 then
                    k=k-1
                end
                user.助战[#user.助战].装备数据.锦衣[k]=临时道具[v]
            end
        end
    end
    发送数据(user.连接id, 420001,user.助战)
end
function 助战处理类:刷新指定数据(user, 编号, 信息, ver)
    if 信息 == "入队退队" then
        -- 将状态标准化为客户端能识别的状态
        local 状态 = user.助战[编号].状态
        if 状态 == "退队" then
            状态 = "出队"
        end
        发送数据(user.连接id, 430004, {编号, 状态, ver})
    end
end
function 助战处理类:更换操作主角(user,转换id)
      if user.id==转换id then
         return
      end

      -- 检查目标角色是否存在
      if not 玩家数据[转换id] then
          常规提示(user.id,"目标角色不存在或已下线")
          return
      end

      -- 检查目标角色是否为助战角色
      if not 玩家数据[转换id].助战识别 then
          常规提示(user.id,"只能切换到助战角色")
          return
      end

      -- 检查目标角色是否在同一队伍中
      if user.队伍 == 0 or 玩家数据[转换id].队伍 ~= user.队伍 then
          常规提示(user.id,"目标角色不在同一队伍中")
          return
      end

      -- 取消观看召唤兽的辅助函数
      local function 取消观看召唤兽(角色id, 提示信息)
          if 玩家数据[角色id].角色.观看召唤兽 then
              -- 获取召唤兽编号
              local 编号 = 玩家数据[角色id].召唤兽:取编号(玩家数据[角色id].角色.观看召唤兽)
              if 编号 > 0 and 玩家数据[角色id].召唤兽.数据[编号] then
                  -- 清除召唤兽的观看状态
                  玩家数据[角色id].召唤兽.数据[编号].观看 = nil
              end

              -- 清除玩家的观看召唤兽状态
              玩家数据[角色id].角色.观看召唤兽 = nil

              -- 通知客户端取消观看召唤兽
              if 玩家数据[角色id].连接id then
                  发送数据(玩家数据[角色id].连接id, 1040, nil)
              end

              if 提示信息 then
                  print(提示信息)
              end

              return true
          end
          return false
      end

      -- 在切换前自动取消观看召唤兽
      取消观看召唤兽(user.id, "更换操作主角前自动取消观看召唤兽")

      网络ID[玩家数据[user.id].角色.连接id]=转换id
      for k,v in pairs(队伍数据[user.id].成员数据) do
            玩家数据[v].角色:存档()
      end
      if 玩家数据[转换id].账号==user.账号 then
         玩家数据[转换id].连接id=user.连接id+0
         玩家数据[转换id].角色.连接id=user.连接id
         玩家数据[转换id].助战识别=nil
         玩家数据[转换id].对接id=nil
         玩家数据[转换id].账号=user.账号

         -- 在切换后确保新角色没有观看召唤兽状态
         取消观看召唤兽(转换id, "确保切换后的角色没有观看召唤兽状态")

         助战处理类:加载数据(玩家数据[转换id])
         -- 只在首次切换时保存聊天记录，避免重复保存
         if not user.聊天记录已保存 then
             发送数据(user.连接id, 420010, {操作 = "保存聊天记录"})
             user.聊天记录已保存 = true
         end
        local 地图数据 = user.角色.地图数据
        玩家数据[转换id].地图 = 地图数据.编号
        玩家数据[转换id].角色.地图数据 = {编号 = 地图数据.编号, x = 地图数据.x, y = 地图数据.y}
         user.连接id=user.id+1000000
         玩家数据[user.id].连接id=user.id+1000000
         玩家数据[user.id].对接id=玩家数据[转换id].连接id
         玩家数据[user.id].助战识别=1
         发送数据(玩家数据[转换id].连接id, 303, {"底图框", "临时背包闪烁", false})
         发送数据(玩家数据[转换id].连接id,5,玩家数据[转换id].角色:取总数据())
         玩家数据[转换id].道具:索要道具3(id,转换id)
         发送数据(玩家数据[转换id].连接id,16,玩家数据[转换id].召唤兽.数据)
         发送数据(玩家数据[转换id].连接id,25,宝宝队伍图)
         发送数据(玩家数据[转换id].连接id,997,{id=转换id,用户="正式用户",名称=玩家数据[转换id].角色.名称,账号=玩家数据[转换id].账号})
         时辰函数()
         发送数据(玩家数据[转换id].连接id,433,时辰信息.天气)
         发送数据(玩家数据[转换id].连接id, 1000)
         玩家数据[转换id].角色:更新任务()
         玩家数据[转换id].角色:刷新任务跟踪()
         玩家数据[转换id].角色:取快捷技能(转换id)
         系统处理类:进入事件(转换id,玩家数据[转换id].连接id,true) -- 更换操作主角时不显示IP
         玩家数据[转换id].角色:刷新信息()
         常规提示(转换id,"切换成功")
         地图处理类:加入玩家(转换id,玩家数据[转换id].角色.地图数据.编号,玩家数据[转换id].角色.地图数据.x,玩家数据[转换id].角色.地图数据.y)
         队伍处理类:新任队长(user.id,转换id)

         -- 如果在战斗中，需要更新战斗数据
         if 玩家数据[转换id].战斗 ~= 0 and 战斗准备类.战斗盒子[玩家数据[转换id].战斗] then
             local 战斗实例 = 战斗准备类.战斗盒子[玩家数据[转换id].战斗]
             -- 更新参战玩家数据中的连接ID和对接关系
             for k, v in pairs(战斗实例.参战玩家) do
                 if 玩家数据[v.id] then
                     v.连接id = 玩家数据[v.id].连接id
                     v.对接id = 玩家数据[v.id].对接id
                 end
             end
             -- 强制刷新自动数据以同步队伍变更
             战斗实例:刷新自动数据()
         end

         -- 恢复聊天记录
         发送数据(玩家数据[转换id].连接id, 420011, {操作 = "恢复聊天记录"})
        local 临时背包数据 = 玩家数据[转换id].道具:临时背包索取()
        if 临时背包数据 and 临时背包数据[1] and next(临时背包数据[1].道具) ~= nil then
            发送数据(玩家数据[转换id].连接id, 303, {"底图框", "临时背包闪烁", true})
        end
        if 玩家数据[转换id].角色.观看召唤兽 then
            发送数据(玩家数据[转换id].连接id, 1036, {操作 = "重置召唤兽状态"})
        end
      else
           常规提示(user.id,"该队友与你不是同账号,无法直接切换操作")
      end
end


return 助战处理类
