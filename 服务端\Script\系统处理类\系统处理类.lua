-- @Author: 小九呀丶 - QQ：5268416
-- @Date:   2024-05-12 22:27:45
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-06-15 20:09:48

function 添加ID特效1(id)
    if not 玩家数据[id] or not 玩家数据[id].角色 or not 玩家数据[id].角色.id特效 then
        return ""
    end
    
    local dfgdfgf = {
        ["黄金ID"] = {[1]="#js1",[2]="#js2",[3]="#js3",[4]="#js4",[5]="#js5",[6]="#js6",[7]="#js7",[8]="#js8",[9]="#js9",[0]="#js0"},
        ["紫色ID"] = {[1]="#zs1",[2]="#zs2",[3]="#zs3",[4]="#zs4",[5]="#zs5",[6]="#zs6",[7]="#zs7",[8]="#zs8",[9]="#zs9",[0]="#zs0"},
        ["绿色ID"] = {[1]="#ls1",[2]="#ls2",[3]="#ls3",[4]="#ls4",[5]="#ls5",[6]="#ls6",[7]="#ls7",[8]="#ls8",[9]="#ls9",[0]="#ls0"},
        ["蓝色ID"] = {[1]="#ns1",[2]="#ns2",[3]="#ns3",[4]="#ns4",[5]="#ns5",[6]="#ns6",[7]="#ns7",[8]="#ns8",[9]="#ns9",[0]="#ns0"}
    }
    
    local tx = 玩家数据[id].角色.id特效
    if dfgdfgf[tx] then
        local hdgge = "("
        local id_str = tostring(id)
        for i = 1, string.len(id_str) do
            local numb = tonumber(string.sub(id_str, i, i))
            hdgge = hdgge .. (dfgdfgf[tx][numb] or numb or "")
        end
        return hdgge .. ")"
    end
    return ""
end

function 添加ID特效(id, 内容)
    return 添加ID特效1(id) .. (内容 or "")
end

local 系统处理类 = class()
function 系统处理类:初始化()
  self.上次更新时间 = os.time()
end

function 系统处理类:更新()
  -- 每秒更新一次
  if os.time() - self.上次更新时间 >= 1 then
    -- 更新地图处理类
    地图处理类:更新()

    -- 更新上次更新时间
    self.上次更新时间 = os.time()
  end
end
function 系统处理类:数据处理(id,序号,内容)
	if 序号==1 then
		self:账号验证(id,序号,内容)
	elseif 序号==1.1 then
		if 注册kaiguan==false then
			发送数据(id,7,"#Y/注册账号需与管理员联系。")
			return
		end
		self:创建账号(id,序号,内容)
	elseif 序号==2 then
		if f函数.文件是否存在(程序目录..[[data/]]..内容.账号)==false then
			发送数据(id,3)
		else
			self.临时文件=读入文件(程序目录..[[data/]]..内容.账号..[[/信息.txt]])
			self.写入信息=table.loadstring(self.临时文件)
			if #self.写入信息>=6 then
				发送数据(id,7,"#Y/您无法再创建更多的角色了")
				return 0
			end
			发送数据(id,3)
		end
	elseif 序号==3 then
		for k,v in pairs(名称数据) do
			if 内容.名称==v.名称 then
				发送数据(id,7,"#Y/这个名称已经被他人占用了，请重新再想个吧")
				return
			end
		end
		if f函数.文件是否存在(程序目录..[[data/]]..内容.账号)==false then
			if lfs.mkdir(程序目录..[[data/]]..内容.账号)==false then
				发送数据(id,7,"#Y/建立存档失败，错误代号1001")
				return 0
			end
			写出文件([[data/]]..内容.账号..[[/账号信息.txt]],"")
			临时角色=角色处理类.创建()
			临时角色:创建角色(id,内容.账号,内容.模型,内容.名称,内容.ip,内容.染色ID)
			名称数据[#名称数据+1]={名称=内容.名称,id=全局角色id,账号=内容.账号}
			写出文件([[游戏数据/名称数据.txt]],table.tostring(名称数据))
			临时角色=nil
			self:取角色选择信息(id,内容.账号)
		else
			self.临时文件=读入文件(程序目录..[[data/]]..内容.账号..[[/信息.txt]])
			self.写入信息=table.loadstring(self.临时文件)
			if #self.写入信息>=6 then
				发送数据(id,7,"#Y/您无法再创建更多的角色了")
				return 0
			end
			临时角色=角色处理类.创建()
			临时角色:创建角色(id,内容.账号,内容.模型,内容.名称,内容.ip,内容.染色ID)
			名称数据[#名称数据+1]={名称=内容.名称,id=全局角色id,账号=内容.账号}
			写出文件([[游戏数据/名称数据.txt]],table.tostring(名称数据))
			临时角色=nil
			self:取角色选择信息(id,内容.账号)
		end
	elseif 序号==4 then
		self:进入游戏(id,内容.账号,内容.id,内容.ip)
	elseif 序号==5 then
		self:断开游戏(内容+0)
	elseif 序号==6 then
		if 玩家数据[内容.数字id].角色.宠物.领养次数==0 then
			玩家数据[内容.数字id].角色.宠物={模型=内容.cw,名称=内容.cw,等级=1,最大等级=120,耐力=5,最大耐力=5,经验=1,最大经验=10,领养次数=1}
			发送数据(id,7,"#Y/领养宠物成功")
			发送数据(id,8,玩家数据[内容.数字id].角色.宠物)
		else
			发送数据(id,7,"#Y/您当前无法再领取宠物了")
		end
	elseif 序号==7 then
		发送数据(id,10,玩家数据[内容.数字id].角色:取总数据1())
	elseif 序号==8 then
		玩家数据[内容.数字id].角色:添加属性点(内容,id)
		发送数据(id,10,玩家数据[内容.数字id].角色:取总数据1())
	elseif 序号==9 then
		玩家数据[内容.数字id].角色:升级处理(id)
	elseif 序号==10 then
		玩家数据[内容.数字id].角色:获取任务信息(id)
	elseif 序号==11 then
		玩家数据[内容.数字id].角色:设置快捷技能(内容)
	elseif 序号==11.1 then
		if 玩家数据[内容.数字id].角色.坐骑==nil then
			常规提示(内容.数字id,"#Y/请先骑乘你要飞的坐骑")
			return
		end
		local 序号=1
		if 内容.位置=="F1" then
			序号=1
		elseif 内容.位置=="F2" then
			序号=2
		elseif 内容.位置=="F3" then
			序号=3
		elseif 内容.位置=="F4" then
			序号=4
		elseif 内容.位置=="F5" then
			序号=5
		elseif 内容.位置=="F6" then
			序号=6
		elseif 内容.位置=="F7" then
			序号=7
		elseif 内容.位置=="F8" then
			序号=8
		end
		玩家数据[内容.数字id].角色:设置快捷技能({名称=" 飞行 ",类型=4,位置=序号})
		常规提示(内容.数字id,"#Y/设置成功...")
	elseif 序号==11.2 then
		玩家数据[内容.数字id].角色:坐骑主属性选择(内容)
		return
	elseif 序号==11.3 then
		玩家数据[内容.数字id].角色:坐骑放生(内容[1])
		return
	elseif 序号==12 then
		发送数据(id,41,玩家数据[内容.数字id].角色.快捷技能)
	elseif 序号==13 then
		玩家数据[内容.数字id].角色:使用快捷技能(内容.序列)
	elseif 序号==14 then
		local id=内容.数字id
		if 内容.人物~=nil then
			玩家数据[id].角色.修炼.当前=内容.人物
		end
		if 内容.bb~=nil then
			玩家数据[id].角色.bb修炼.当前=内容.bb
		end
		常规提示(id,"#Y/更换修炼类型成功！")
		刷新修炼数据(id)
	elseif 序号==15 then
		local id=内容.数字id
		if 玩家数据[id].角色.自动遇怪 then
			玩家数据[id].角色.自动遇怪=false
			常规提示(id,"#Y/你关闭了自动遇怪功能")
			自动遇怪[id]=nil
		else
			玩家数据[id].角色.自动遇怪=true
			常规提示(id,"#Y/你开启了自动遇怪功能，每隔30秒会自动触发野外场景暗雷战斗。处于自动遇怪模式下人物将无法进行移动、对话操作。如需关闭此功能，再次勾选自动遇怪即可关闭此功能。")
			自动遇怪[id]=os.time()
		end
		发送数据(玩家数据[id].连接id,48,{遇怪=玩家数据[id].角色.自动遇怪})
	elseif 序号==16 then
		if 好友消息类:取消息数据(内容.数字id,玩家数据[内容.数字id].连接id)==false then
			玩家数据[内容.数字id].角色:取好友数据(内容.数字id,玩家数据[内容.数字id].连接id,50)
		end
	elseif 序号==26 then
		local 序列=内容.序列
		local id=内容.数字id
		if 玩家数据[id].角色.坐骑列表[序列]==nil then
			常规提示(id,"#Y你没有这样的坐骑")
			return
		end
		玩家数据[id].角色.坐骑=table.copy(玩家数据[id].角色.坐骑列表[序列])
		常规提示(id,"#Y骑乘坐骑成功！")
		玩家数据[id].角色:刷新信息("33")
		发送数据(玩家数据[id].连接id,60,玩家数据[id].角色.坐骑)
		地图处理类:更新坐骑(id,玩家数据[id].角色.坐骑)
	elseif 序号==34.1 then
		if 内容[1] == "乌鸡国" then
			任务处理类:副本传送(内容.数字id,1)
		elseif 内容[1] == "水陆大会" then
			任务处理类:副本传送(内容.数字id,2)
		elseif 内容[1] == "车迟斗法" then
			任务处理类:副本传送(内容.数字id,3)
		elseif 内容[1] == "泾河龙王2" then
			任务处理类:副本传送(内容.数字id,14)
		elseif 内容[1] == "红孩儿" then
			任务处理类:副本传送(内容.数字id,7)
		elseif 内容[1] == "七绝山" then
			任务处理类:副本传送(内容.数字id,5)
		elseif 内容[1] == "天火之殇上部" then
			任务处理类:副本传送(内容.数字id,950)
		elseif 内容[1] == "齐天大圣" then
			任务处理类:副本传送(内容.数字id,581)
		elseif 内容[1] == "秘境降妖" then
			任务处理类:副本传送(内容.数字id,90)
		elseif 内容[1] == "无底洞" then
			任务处理类:副本传送(内容.数字id,906)
		elseif 内容[1] == "黑风山" then
			任务处理类:副本传送(内容.数字id,80)
		elseif 内容[1] == "大闹天宫" then
			任务处理类:副本传送(内容.数字id,70)
		elseif 内容[1] == "通天河" then
			任务处理类:副本传送(内容.数字id,710)
		else
			常规提示(id,"#Y/该类副本尚未开放")
		end
	elseif 序号==26.1 then
		local 序列=内容.序列
		local id=内容.数字id
		if 玩家数据[id].角色.坐骑列表[序列]==nil then
			常规提示(id,"#Y你没有这样的坐骑")
			return
		end
		玩家数据[id].角色.坐骑列表[序列].饰品 = nil
	elseif 序号==27 then
		local id=内容.数字id
		if 玩家数据[id].队伍~=0 then
			if 玩家数据[id].角色.飞行中 then
				常规提示(id,"#Y队伍飞行中禁止此操作！")
				return
			end
		else
			if 玩家数据[id].角色.飞行中 then
				玩家数据[id].角色.飞行中=nil
				常规提示(id,"#Y/你落地了...")
				地图处理类:玩家是否飞行(id,false)
			end
		end
		玩家数据[id].角色.坐骑=nil
		常规提示(id,"#Y下乘坐骑成功！")
		玩家数据[id].角色:刷新信息("33")
		发送数据(玩家数据[id].连接id,60,玩家数据[id].角色.坐骑)
		地图处理类:更新坐骑(id,玩家数据[id].角色.坐骑)
	elseif 序号==28 then
		玩家数据[内容.数字id].角色:取快捷技能(内容.数字id)
	elseif 序号==29 then
		self:自动抓鬼处理(内容)
	elseif 序号==29.1 then
		if 玩家数据[内容.数字id].角色.自动抓鬼==nil then
			玩家数据[内容.数字id].角色.自动抓鬼=0
		end
		玩家数据[内容.数字id].自动抓鬼 = 内容.事件
		发送数据(玩家数据[内容.数字id].连接id,220,{进程 = "第六进程",事件=内容.事件})
	elseif 序号==30 then
		商城处理类:数据处理(内容.数字id, 内容)
	elseif 序号==30.1 then
		if 内容.分类~=nil then
			商城处理类:取商品组数据(内容.数字id, 18,内容.分类)
		else
			商城处理类:取商品组数据(内容.数字id, 18)
		end
	elseif 序号==31 then
		地图处理类:更新称谓(内容.数字id,内容.称谓ID)
	elseif 序号==74 then
		玩家数据[内容.数字id].角色:删除称谓(内容.称谓)
	elseif 序号==32 then
		玩家数据[内容.数字id].角色:增加奇经八脉(内容.数字id,内容.序列)
	elseif 序号==33 then
	elseif 序号==34 then
		玩家数据[内容.数字id].角色:炼化乾元丹(内容.数字id)
	elseif 序号==35 then
		帮派处理:创建帮派(内容.数字id,内容)
	elseif 序号==36 then
		local bpbh = 玩家数据[内容.数字id].角色.BPBH
		if 帮派数据[bpbh] and 取所有帮派[bpbh].已解散==nil then
			发送数据(玩家数据[内容.数字id].连接id,205,帮派数据[bpbh])
		else
			if 玩家数据[内容.数字id].角色.BPMC == "无帮派" then
				帮派处理:加入帮派(内容.数字id)
			else
				常规提示(内容.数字id,"你已经有一个帮派了")
			end
		end
	elseif 序号==37 then
		发送数据(玩家数据[内容.数字id].连接id,103)
	elseif 序号==37.1 then
		帮派处理:解散帮派(内容.数字id)
	elseif 序号==37.2 then
		帮派处理:退出帮派(内容.数字id)
	elseif 序号==38 then
		帮派处理:加入帮派2(内容.数字id,内容.编号)
	elseif 序号==38.1 then
		帮派处理:取消申请(内容.数字id,内容.编号)
	elseif 序号==39 then
		if 内容.类型 == 1 then
		elseif 内容.类型 == 2 then
			发送数据(玩家数据[内容.数字id].连接id,49.1,{数据=排行数据.积分排行,类型="积分排行"})
		elseif 内容.类型 == 3 then
			发送数据(玩家数据[内容.数字id].连接id,49.1,{数据=排行数据.属性排行,类型="属性排行"})
		elseif 内容.类型 == 4 then
			发送数据(玩家数据[内容.数字id].连接id,49.1,{数据=排行数据.数据排行,类型="数据排行"})
		else
			发送数据(玩家数据[内容.数字id].连接id,49,排行数据.属性排行)
			发送数据(玩家数据[内容.数字id].连接id,49.1,{数据=排行数据.数据排行,类型="数据排行"})
		end
	elseif 序号==40 then
		帮派处理:清空申请(内容.数字id)
	elseif 序号==41 then
		帮派处理:玩家数据变更(内容)
	elseif 序号==42 then
		帮派处理:帮派建筑研究(内容)
	elseif 序号==43 then
		帮派处理:帮派规模提升(内容.数字id)
	elseif 序号==44 then
		if 内容.免资材 and 内容.免资材 ~= false then
			玩家数据[内容.数字id].角色:帮派免资材学习修炼(内容.类型,内容.修炼项目)
		else
			玩家数据[内容.数字id].角色:帮派学习修炼(内容.类型,内容.修炼项目)
		end
	elseif 序号==45 then
		玩家数据[内容.数字id].角色:帮派学习生活技能(内容.序列)
	elseif 序号==46 then
		if 玩家数据[内容.数字id].角色:扣除银子(30000000,0,0,"人物炫彩",1)  then
			if 玩家数据[内容.数字id].角色.炫彩 == nil then
				玩家数据[内容.数字id].角色.炫彩 = 玩家数据[内容.数字id].角色.染色方案
				玩家数据[内容.数字id].角色.炫彩组 ={}
			end
			玩家数据[内容.数字id].角色.炫彩组=内容.炫彩
			常规提示(内容.数字id,"角色染色成功")
			发送数据(玩家数据[内容.数字id].连接id,213,{炫彩=玩家数据[内容.数字id].角色.炫彩,炫彩组=玩家数据[内容.数字id].角色.炫彩组})
			地图处理类:更改炫彩(内容.数字id,玩家数据[内容.数字id].角色.炫彩,玩家数据[内容.数字id].角色.炫彩组)
		end
	elseif 序号==46.1 then --武器炫彩
        local id = 内容.数字id

        if 玩家数据[id].角色.装备[3] == nil or 玩家数据[id].角色.装备[3] == 0 then
            发送数据(玩家数据[id].连接id, 1501, {名称 = "袁天罡", 模型 = "袁天罡", 对话 = "少侠是来寻我开心的么？你要染色的武器呢！"})
            return
        end
        local 道具id = 玩家数据[id].角色.装备[3]

        if 玩家数据[id].道具.数据[道具id].染色方案 == nil then
            玩家数据[id].道具.数据[道具id].染色方案 = 0
        end
        if 玩家数据[id].道具.数据[道具id].染色组 == nil then
            玩家数据[id].道具.数据[道具id].染色组 = {}
        end
        玩家数据[id].道具.数据[道具id].染色方案 = 内容[1]
        玩家数据[id].道具.数据[道具id].染色组[1] = 内容[2]
        玩家数据[id].道具.数据[道具id].染色组[2] = 内容[3]
        常规提示(id, "恭喜你，武器染色成功！快去看看武器的新造型吧！")
        道具刷新(id)
        地图处理类:更新武器(id, 玩家数据[id].道具.数据[玩家数据[id].角色.装备[3]])
	elseif 序号==47 then --取消玄彩
		if 内容.shij=="角色焕彩" then
			玩家数据[内容.数字id].角色.炫彩=nil
			玩家数据[内容.数字id].角色.炫彩组 =nil
			常规提示(内容.数字id,"还原焕彩成功，重新上线即可生效")
		elseif 内容.shij=="坐骑焕彩" then
			local 临时编号=玩家数据[内容.数字id].角色:取坐骑编号(内容.认证码)
			if 临时编号==0 then
				常规提示(内容.数字id,"你没有这只坐骑")
				return
			else
				玩家数据[内容.数字id].角色.坐骑列表[临时编号].炫彩 =nil
				玩家数据[内容.数字id].角色.坐骑列表[临时编号].炫彩组 = nil
				常规提示(内容.数字id,"还原焕彩成功，重新上线即可生效")
				if 玩家数据[内容.数字id].角色.坐骑 and 玩家数据[内容.数字id].角色.坐骑.认证码 == 内容.认证码 then
					玩家数据[内容.数字id].角色.坐骑=table.loadstring(table.tostring(玩家数据[内容.数字id].角色.坐骑列表[临时编号]))
				end
			end
		elseif 内容.shij=="召唤兽焕彩" then
			local 临时编号=玩家数据[内容.数字id].召唤兽:取编号(内容.认证码)
			if 临时编号==0 then
				常规提示(内容.数字id,"你没有这只召唤兽")
				return
			else
				玩家数据[内容.数字id].召唤兽.数据[临时编号].染色方案 = nil
				玩家数据[内容.数字id].召唤兽.数据[临时编号].染色组 = nil
				常规提示(内容.数字id,"还原焕彩成功")
			end
		elseif 内容.shij=="武器焕彩" then
	 		local id = 内容.数字id
	        local 道具id = 玩家数据[id].角色.装备[3]

	        if 道具id == nil or 道具id == 0 then
	            常规提示(id, "你没有装备武器")
	            return
	        end
	        -- 清除武器的染色方案和染色组
	        if 玩家数据[id].道具.数据[道具id].染色方案 ~= nil then
	            玩家数据[id].道具.数据[道具id].染色方案 = nil
	        end

	        if 玩家数据[id].道具.数据[道具id].染色组 ~= nil then
	            玩家数据[id].道具.数据[道具id].染色组 = nil
	        end

	        常规提示(id, "武器特效已取消，重新穿戴即可生效")
	        道具刷新(id)
	        地图处理类:更新武器(id, 玩家数据[id].道具.数据[道具id])
		end

	elseif 序号==67 then
		if 玩家数据[内容.数字id].角色:扣除银子(50000000,0,0,"坐骑炫彩",1)  then
			local 临时编号=玩家数据[内容.数字id].角色:取坐骑编号(内容.认证码)
			if 临时编号==0 then
				常规提示(内容.数字id,"你没有这只坐骑")
				return
			else
				if 玩家数据[内容.数字id].角色.坐骑列表[临时编号].炫彩 == nil then
					玩家数据[内容.数字id].角色.坐骑列表[临时编号].炫彩 = 内容.染色id
					玩家数据[内容.数字id].角色.坐骑列表[临时编号].炫彩组 = {}
				end
				玩家数据[内容.数字id].角色.坐骑列表[临时编号].炫彩组 = 内容.炫彩
				常规提示(内容.数字id,"坐骑染色成功")
				发送数据(玩家数据[内容.数字id].连接id,61,玩家数据[内容.数字id].角色.坐骑列表)
				if 玩家数据[内容.数字id].角色.坐骑 and 玩家数据[内容.数字id].角色.坐骑.认证码 == 内容.认证码 then
					玩家数据[内容.数字id].角色.坐骑=table.loadstring(table.tostring(玩家数据[内容.数字id].角色.坐骑列表[临时编号]))
				end
				地图处理类:更新坐骑(内容.数字id,玩家数据[内容.数字id].角色.坐骑)
			end
		end
	elseif 序号==48 then
		local num=内容.JQ+0
		local id=内容.数字id
		if 体验状态开关 and 玩家数据[id].角色.体验状态 then
			常规提示(id,"体验状态下无法进行此操作。")
			return
		end
		if num<0 or (num~=0 and num<1)  then
			__S服务:输出("玩家"..id.." 存在作弊行为！！！钱庄存钱")
			写配置("./游戏记录/ip封禁.ini", "ip", 玩家数据[id].角色.ip, 1)
			f函数.写配置(程序目录..[[data\]]..玩家数据[id].账号..[[\账号信息.txt]],"账号配置","封禁","1")
			local 封禁原因=玩家数据[id].角色.ip.."“违规行为：钱庄存钱”存银=="..num.."，玩家ID=="..id.."，玩家账号=="..玩家数据[id].账号
			f函数.写配置(程序目录..[[data\]]..玩家数据[id].账号..[[\账号信息.txt]],"账号配置","已违规1",封禁原因)
			发送数据(玩家数据[id].连接id, 998, "请注意你的角色异常！已经对你进行封禁")
			__S服务:断开连接(玩家数据[id].连接id)
			return
		end
		num=qz(num)
		if 玩家数据[id].角色:扣除银子(num,0,0,"存款") then
			玩家数据[id].角色.存银=玩家数据[id].角色.存银+num
			常规提示(id,"您已存入"..num.."两银子")
			if 玩家数据[id].角色.存银>100000000*50 then
				发送系统消息(id,"#W/您身上的银子已经超过上限，多余的银两将在下次登陆的时自动捐赠大唐金库用作国家发展。")
			end
			道具刷新(id)
		else
			常规提示(id,"你身上也没那么多钱啊")
		end
	elseif 序号==49 then
		local num=内容.JQ+0
		local id=内容.数字id
		if num<0 or (num~=0 and num<1) then
			__S服务:输出("玩家"..id.." 存在作弊行为！！！钱庄取钱")
			写配置("./游戏记录/ip封禁.ini", "ip", 玩家数据[id].角色.ip, 1)
			f函数.写配置(程序目录..[[data\]]..玩家数据[id].账号..[[\账号信息.txt]],"账号配置","封禁","1")
			local 封禁原因=玩家数据[id].角色.ip.."“违规行为：钱庄取钱”取钱=="..num.."，玩家ID=="..id.."，玩家账号=="..玩家数据[id].账号
			f函数.写配置(程序目录..[[data\]]..玩家数据[id].账号..[[\账号信息.txt]],"账号配置","已违规2",封禁原因)
			发送数据(玩家数据[id].连接id, 998, "请注意你的角色异常！已经对你进行封禁")
			__S服务:断开连接(玩家数据[id].连接id)
			return
		end
		num=qz(num)
		if 玩家数据[id].角色.存银>=num then
			玩家数据[id].角色.存银=玩家数据[id].角色.存银-num
			玩家数据[id].角色:添加银子(num,"取款",1)
			常规提示(id,"您已取出"..num.."两银子")
			道具刷新(id)
		else
			常规提示(id,"存银不足无法取出")
		end
	elseif 序号==50 then
		玩家数据[内容.数字id].角色:重置奇经八脉()
	elseif 序号==55 then
		__S服务:输出(string.format('尝试作弊使用非法数据(%s)',内容.数字id))
	elseif 序号==56 then
	elseif 序号==57 then
		if 玩家数据[内容.数字id].队伍~=0 then
			常规提示(内容.数字id,"#Y/队伍中无法进行此项操作")
			return
		end
		if 玩家数据[内容.数字id].角色.等级 < 10 then
			常规提示(内容.数字id,"#Y/请先将等级提升到10级")
		else
			地图处理类:门派传送(内容.数字id,内容.门派)
			local lssj =  取师傅地图(内容.门派)
			玩家数据[内容.数字id].角色:增加剧情点(1)
			玩家数据[内容.数字id].角色.剧情={主线=2,编号 = 1,地图 = lssj,进度 = 2222,附加={}}
			发送数据(玩家数据[内容.数字id].连接id,227,{剧情=玩家数据[内容.数字id].角色.剧情})
		end
	elseif 序号==59 then
		玩家数据[内容.数字id].角色:坐骑统御(内容)
	elseif 序号==59.1 then
		玩家数据[内容.数字id].角色:取消坐骑统御(内容)
	elseif 序号==60 then
		玩家数据[内容.数字id].角色:坐骑改名(内容)
	elseif 序号==61 then
		玩家数据[内容.数字id].角色:坐骑驯养(内容)
	elseif 序号==64.1 then
		local id = 内容.数字id
		if 内容.类型 == "生活" then
			玩家数据[id].给予数据={类型=1,id=内容.编号,事件="孩子生活"}
			发送数据(玩家数据[id].连接id,3507,{道具=玩家数据[id].道具:索要道具1(id),名称="孩子生活",类型="NPC",等级="无"})
		elseif 内容.类型 == "学习" then
			玩家数据[id].给予数据={类型=1,id=内容.编号,事件="孩子学习"}
			发送数据(玩家数据[id].连接id,3507,{道具=玩家数据[id].道具:索要道具1(id),名称="孩子学习",类型="NPC",等级="无"})
		end
	elseif 序号==65.2 then
		local id = 内容.数字id
		local 编号 = 内容.编号
		if 玩家数据[id].孩子.数据[编号] ~= nil then
			if os.time() - 玩家数据[id].孩子.数据[编号].出生日期<=1 then
				常规提示(id,"#Y该孩子尚未成年哟!!!")
				return
			elseif 玩家数据[id].孩子.数据[编号].成年 ~= nil or 玩家数据[id].孩子.数据[编号].门派 ~= nil then
				常规提示(id,"#Y该孩子举办过成年礼了哟!!!")
				return
			end
			local 选项 = {}
			if 玩家数据[id].孩子.数据[编号].模型 == "小毛头" or 玩家数据[id].孩子.数据[编号].模型 == "小丫丫" then
				选项 = {"大唐官府","化生寺","女儿村","方寸山"}
			elseif 玩家数据[id].孩子.数据[编号].模型 == "小仙女" or 玩家数据[id].孩子.数据[编号].模型 == "小仙灵" then
				选项 = {"天宫","普陀山","龙宫","五庄观"}
			elseif 玩家数据[id].孩子.数据[编号].模型 == "小魔头" or 玩家数据[id].孩子.数据[编号].模型 == "小精灵" then
				选项 = {"狮驼岭","魔王寨","阴曹地府","盘丝洞"}
			end
			玩家数据[id].孩子操作 = 编号
			local 对话=[[请为您的孩子选择一个喜爱的门派加入吧！一旦加入门派无法进行改变！]]
			发送数据(玩家数据[id].连接id,1501,{名称=玩家数据[id].角色.名称,模型=玩家数据[id].角色.模型,对话=对话,选项=选项})
		end
	elseif 序号==66.3 then
		local id = 内容.数字id
		local 编号 = 内容.编号
		if 玩家数据[id].孩子.数据[编号] ~= nil then
			local 总点数 = 0
			for i=1,5 do
				if 内容.加点方式[i] ~= nil and tonumber(内容.加点方式[i])~=nil then
					总点数 = 总点数 + 内容.加点方式[i]
				end
			end
			if 总点数 ~= 5 then
				常规提示(id,"#Y加点设置有误,点数总和应当等于5")
				return
			end
			玩家数据[id].孩子.数据[编号].加点方案 = 内容.加点方式
			常规提示(id,"#Y加点设置成功")
		end
		if 玩家数据[id].孩子.数据[编号].等级 <= 0 then
			玩家数据[id].孩子:重置等级(10,编号)
			发送数据(玩家数据[id].连接id,96.2,{数据=玩家数据[id].孩子.数据[编号],编号=编号})
		end
	elseif 序号==67.4 then
		local id = 内容.数字id
		local 编号 = 内容.编号
		if 玩家数据[id].孩子.数据[编号].领取 == nil or 玩家数据[id].孩子.数据[编号].领取 ~= os.date("%d", os.time()) then
			玩家数据[id].孩子:每日奖励(id,编号)
		else
			常规提示(id,"#Y今日奖励已经领取")
		end
		return
	elseif 序号==62 then
		if 内容.数字id then
			写配置(".游戏记录/加速齿轮.txt","加速齿轮",内容.ip,"加速外挂 ID为 "..内容.数字id..时间转换(os.time()))
		end
		return
	elseif 序号==63 then
		玩家数据[内容.数字id].角色:坐骑技能升级(内容)
	elseif 序号==64 then
		玩家数据[内容.数字id].角色:切换经脉流派(内容.数字id,内容.新流派,内容.旧流派)
	elseif 序号==66 then
		local id=内容.数字id
		仙玉处理类:仙玉寄售(内容.数字id,内容)
	elseif 序号==66.1 then
		local id=内容.数字id
		仙玉处理类:仙玉取回(内容.数字id,内容)
	elseif 序号==66.2 then
		仙玉处理类:仙玉购买(内容.数字id,内容)
	elseif 序号==68 then
	elseif 序号 == 69 then
	elseif 序号==69.1 then
		self:召唤兽携带上限(内容.数字id)
	elseif 序号==70 then
		if  玩家数据[内容.数字id].角色.招式特效 ~= nil and 玩家数据[内容.数字id].角色.招式特效[内容.名称] ~= nil then
			if 玩家数据[内容.数字id].角色.招式特效[内容.名称] then
				玩家数据[内容.数字id].角色.招式特效[内容.名称] = false
				发送数据(id,7,"#Y/技能#R"..内容.名称.."#Y招式特效已#R关闭")
			else
				玩家数据[内容.数字id].角色.招式特效[内容.名称] = true
				发送数据(id,7,"#Y/技能#R"..内容.名称.."#Y招式特效已#G开启")
			end
			发送数据(玩家数据[内容.数字id].连接id,236,{名称=内容.名称,状态=玩家数据[内容.数字id].角色.招式特效[内容.名称]})
		end
	elseif 序号==71 then
		local 名称=内容.名称
		local 检测=内容.检测
		local 数字id=内容.数字id
		for k,v in pairs(名称数据) do
			if 内容.名称==v.名称 then
				发送数据(id,7,"#Y/这个名称已经被他人占用了，请重新再想个吧")
				return
			end
		end
		if #名称>12 then
			发送数据(id,7,"#Y/这个名字太长了。")
			return
		end
		if not 检测 then
			if 玩家数据[数字id].角色.改名.时间 < os.time() then
				if 玩家数据[数字id].角色:扣除银子(2000000*(玩家数据[数字id].角色.改名.次数+1), 0, 0, "改名", 1) then
					玩家数据[数字id].角色.名称 = 名称
					table.insert(玩家数据[数字id].角色.改名.曾用名,名称)
					玩家数据[数字id].角色.改名.次数 = 玩家数据[数字id].角色.改名.次数 +1
					玩家数据[数字id].角色.改名.时间 =os.time()+604800
					发送数据(id,237,{名称=名称})
					local bpbh=玩家数据[数字id].角色.BPBH
					for k,v in pairs(名称数据) do
						if 数字id==v.id then
							v.名称=名称
							break
						end
					end
					写出文件([[游戏数据/名称数据.txt]],table.tostring(名称数据))
					if 玩家数据[数字id].角色.BPMC ~= "无帮派" and 帮派数据[bpbh] then
						for k,v in pairs(帮派数据[bpbh].成员数据) do
							if v.id==数字id then
								v.名称=名称
								break
							end
						end
					end
					添加最后对话(数字id,"修改名字成功！")
				else
					发送数据(id,7,"#Y/银两不足无法修改名称")
				end
			else
				发送数据(id,7,"#Y/你距离上次改名时间没有超过7天")
			end
		else
			发送数据(id,7,"#G/当前名字可用。")
		end
		return
	elseif 序号==72 then
		if 内容.名称 == "熔炼技巧"  and 内容.等级 < 60  then
			发送数据(id,7,"#Y/技能等级不足60级")
		elseif 内容.名称 == "熔炼技巧"  and 内容.等级 >= 60  then
			local 等级 =  math.floor(内容.等级/10)*10
			local 消耗 =  等级*0.5-20
			if 玩家数据[内容.数字id].角色.体力 >= 消耗 then
				玩家数据[内容.数字id].角色.体力 = 玩家数据[内容.数字id].角色.体力 -消耗
			else
				发送数据(id,7,"#Y/体力不足无法使用技能")
				return
			end
			体活刷新(内容.数字id)
			玩家数据[内容.数字id].道具:给予道具(内容.数字id,"钨金",1,等级)
		end
	elseif 序号==73 then
		local 目标id=tonumber(内容.文本)
		local id1 = 内容.数字id
		if 玩家数据[目标id]==nil then
			发送数据(id,7,"#Y/该玩家不在线无法查找！")
			玩家数据[id1].最后操作=nil
			玩家数据[id1].法宝id=nil
			return
		end
		if 玩家数据[id1].最后操作=="影蛊" and 玩家数据[id1].法宝id ~=nil then
			local 道具id=玩家数据[id1].角色.法宝[玩家数据[id1].法宝id]
			if 玩家数据[id1].道具.数据[道具id].名称=="影蛊" then
				if 玩家数据[id1].道具.数据[道具id].时间~=nil and 玩家数据[id1].道具.数据[道具id].时间>os.time() then
					常规提示(id1,"#Y该法宝需要在#R"..时间转换(玩家数据[id1].道具.数据[道具id].时间).."#Y后才可使用")
					玩家数据[id1].最后操作=nil
					玩家数据[id1].法宝id=nil
					return
				elseif not 玩家数据[id1].角色:扣除银子(2000000,0,0,"影蛊") then
					常规提示(id1,"#Y需要200W银子才能使用")
					玩家数据[id1].最后操作=nil
					玩家数据[id1].法宝id=nil
					return
				end
				玩家数据[id1].道具.数据[道具id].魔法=玩家数据[id1].道具.数据[道具id].魔法-5
				消息提示(id1,"你的法宝影蛊灵气减少了5点")
				玩家数据[id1].道具.数据[道具id].时间=os.time()+3000-玩家数据[id1].道具.数据[道具id].气血*200
				消息提示(id1,"目标"..玩家数据[目标id].角色.名称.."("..目标id..")位于"..取地图名称(玩家数据[目标id].角色.地图数据.编号).."("..qz(玩家数据[目标id].角色.地图数据.x/20).."，"..qz(玩家数据[目标id].角色.地图数据.y/20)..")")
				道具刷新(id1)
			end
		end
		玩家数据[id1].最后操作=nil
		玩家数据[id1].法宝id=nil
	elseif 序号==80 then
	elseif 序号==81 then
	elseif 序号==88 then
		local id=内容.数字id
		if 内容.文本 ~= 玩家数据[id].最后操作 then
			玩家数据[id].最后操作=nil
			常规提示(id,"#Y抄写经书不可三心二意，抄错了可就不好了！")
			return
		elseif 玩家数据[id].角色:取任务(80) == 0 or 副本数据.黑风山.进行[任务数据[玩家数据[id].角色:取任务(80)].副本id].进程 ~= 1 then
			常规提示(id,"#Y你没有这样的任务！")
			玩家数据[id].最后操作=nil
			return
		else
			玩家数据[id].最后操作=nil
			副本_黑风山:完成黑风山任务({id},玩家数据[id].角色:取任务(80),82)
		end
	elseif 序号==90 then
		玩家数据[内容.数字id].角色.系统设置[内容.选项] = 内容.回调
	elseif 序号==91 then
		玩家数据[内容.数字id].角色:门派转换(内容)
	elseif 序号==92 then
		for n, v in pairs(玩家数据[内容.数字id].角色.快捷技能) do
			if 玩家数据[内容.数字id].角色.快捷技能[n].名称 == 内容.名称 then
				玩家数据[内容.数字id].角色.快捷技能[n] = nil
			end
		end
		发送数据(玩家数据[内容.数字id].连接id, 42, 玩家数据[内容.数字id].角色.快捷技能)
	elseif 序号==93 then
	elseif 序号==94 then
		礼包奖励类:领取新手礼包(内容.数字id,内容.礼包)
	elseif 序号 == 95 then
		抽奖处理:转盘抽奖处理(内容.数字id)
	elseif 序号 == 96 then
		抽奖处理:钥匙宝箱抽奖(内容.数字id,内容)
	elseif 序号 == 97 then
		self:打字机回调(内容.数字id,内容)
	elseif 序号 == 98 then
		self:第二场景回调(内容.数字id,内容)
	elseif 序号 == 99 then
		self:如梦奇谭开启(内容.数字id,内容)
	elseif 序号 == 100 then
		self:购买戏票(内容.数字id,内容)
	elseif 序号 == 100 then
		炼丹查看[内容.数字id] = nil
	elseif 序号 == 100.1 then
		游戏活动类:炼丹下注(内容)
		elseif  序号 == 135.1 then
      self:收获动植物(内容.数字id,内容)
  	elseif  序号 == 135.2 then
      self:扩充种植土地(内容.数字id)
   	elseif  序号 == 135.3 then
      self:提升庭院护卫(内容.数字id)
	elseif 序号 == 101 then
		if 玩家数据[内容.数字id].角色:扣除银子(10000,0,0,"祈福",1)  then
			玩家数据[内容.数字id].角色.祈福 = 玩家数据[内容.数字id].角色.祈福+10
			常规提示(内容.数字id,"#y/您的祈福次数为"..玩家数据[内容.数字id].角色.祈福 )
			发送数据(玩家数据[内容.数字id].连接id,119.1,玩家数据[内容.数字id].角色.祈福)
			广播消息({内容=format("#Y/#g/ "..玩家数据[内容.数字id].角色.名称.."#y/在朱紫国找到神机道长，在祈福石上写下所祈求的事情，心诚则灵，必有上天的庇佑"),频道="xt"})
		else
			常规提示(内容.数字id,"你身上也没那么多钱啊")
		end
	elseif 序号 == 102 then
	    local id=内容.数字id
	    if 玩家数据[id].角色:取任务(55) == 0 or 任务数据[玩家数据[id].角色:取任务(55)].分类~=2 then
	      常规提示(id,"#Y/你没有这样的任务")
	      return
	    elseif 玩家数据[id].最后操作~="月饼造句" then
	      常规提示(id,"#Y/请重新触发对话")
	      return
	    elseif string.find(内容.文本,"月饼") then
	      中秋任务:完成中秋单人任务(id,玩家数据[id].角色:取任务(55))
	    else
	      常规提示(id,"#Y/你不会连个造句都不会吧")
	    end
	elseif 序号 == 102.2 then
		-- 调用处理帮派传送请求函数
		self:处理帮派传送请求(内容, 内容.数字id)
	elseif 序号 == 103 then
		发送数据(玩家数据[内容.数字id].连接id,116,藏宝阁数据)
	elseif 序号 == 103.1 then
		self:藏宝阁上架(内容.数字id,内容.编号,内容.价格)
	elseif 序号 == 103.2 then
		self:藏宝阁取回处理(内容.数字id,内容.编号,内容.类型)
	elseif 序号 == 103.3 then
		self:藏宝阁上架银两(内容.数字id,内容.编号,内容.价格)
	elseif 序号 == 103.4 then
		self:藏宝阁上架召唤兽(内容.数字id,内容.编号,内容.价格)
	elseif 序号 == 103.5 then
		玩家数据[内容.数字id].角色.藏宝阁出售 = 1
		藏宝阁数据.角色[#藏宝阁数据.角色+1] = {所有者=内容.数字id,价格=内容.编号,取回密码=内容.价格,结束时间=os.time()+604800,角色信息={账号=玩家数据[内容.数字id].账号,模型=玩家数据[内容.数字id].角色.模型,名称=玩家数据[内容.数字id].角色.名称,等级=玩家数据[内容.数字id].角色.等级,门派=玩家数据[内容.数字id].角色.门派}}
		玩家数据[内容.数字id].角色:存档()
		发送数据(玩家数据[内容.数字id].连接id,999,"上架角色成功,即日起该角色将无法进入游戏,可以使用任意角色在贸易车队总管使用取回密码取回账号,请牢记你的取回密码！")
		__S服务:连接退出(玩家数据[内容.数字id].连接id)
	elseif 序号 == 103.6 then
		local id = 内容.数字id
		local 编号 = 内容.编号
		local 名称 = 内容.名称
		if 藏宝阁数据.角色[编号] == nil or 名称 ~= 藏宝阁数据.角色[编号].角色信息.名称 then
			常规提示(id,"#Y数据错误请重新打开藏宝阁")
			return
		end
		local 发送角色信息= {}
		发送角色信息.角色数据 = table.loadstring(读入文件([[data/]]..藏宝阁数据.角色[编号].角色信息.账号..[[/]]..藏宝阁数据.角色[编号].所有者..[[/角色.txt]]))
		发送角色信息.召唤兽数据 = table.loadstring(读入文件([[data/]]..藏宝阁数据.角色[编号].角色信息.账号..[[/]]..藏宝阁数据.角色[编号].所有者..[[/召唤兽.txt]]))
		local 道具数据 = table.loadstring(读入文件([[data/]]..藏宝阁数据.角色[编号].角色信息.账号..[[/]]..藏宝阁数据.角色[编号].所有者..[[/道具.txt]]))
		发送角色信息.装备数据 = {}
		for i=1,6 do
			if 发送角色信息.角色数据.装备[i] ~= nil and 道具数据[发送角色信息.角色数据.装备[i]] ~= nil then
				发送角色信息.装备数据[i] = 道具数据[发送角色信息.角色数据.装备[i]]
			end
		end
		发送角色信息.灵饰数据 = {}
		for i=1,4 do
			if 发送角色信息.角色数据.灵饰[i] ~= nil and 道具数据[发送角色信息.角色数据.灵饰[i]] ~= nil then
				发送角色信息.灵饰数据[i] = 道具数据[发送角色信息.角色数据.灵饰[i]]
			end
		end
		发送数据(玩家数据[id].连接id,116.6,发送角色信息)
		发送角色信息 = nil
		道具数据 = nil
	elseif 序号 == 103.7 then
		self:藏宝阁购买处理(内容.数字id,内容.编号,内容.类型)
	elseif 序号==103.8 then
		local id = 内容.数字id
		local 目标id = 内容.编号
		local 密码 = 内容.价格
		local 找到 = false
		for i=1,#藏宝阁数据.角色 do
			if 藏宝阁数据.角色[i].所有者 == 目标id or tonumber(藏宝阁数据.角色[i].所有者) == tonumber(目标id) then
				找到=true
				if 藏宝阁数据.角色[i].取回密码 ~= 密码 then
					常规提示(id,"#Y密码错误请重新输入")
					return
				else
					local 角色信息 = table.loadstring(读入文件([[data/]]..藏宝阁数据.角色[i].角色信息.账号..[[/]]..藏宝阁数据.角色[i].所有者..[[/角色.txt]]))
					角色信息.藏宝阁出售 = nil
					写出文件([[data/]]..藏宝阁数据.角色[i].角色信息.账号..[[/]]..藏宝阁数据.角色[i].所有者..[[/角色.txt]],table.tostring(角色信息))
					角色信息 = nil
					table.remove(藏宝阁数据.角色,i)
					常规提示(id,"#Y恭喜角色成功取回,请原账号登录即可!!!")
					return
				end
			end
		end
		if 找到==false  then
			常规提示(id,"#Y未找到信息!!!")
		end
	elseif 序号 == 105 then
		玩家数据[内容.数字id].角色:兑换潜能果()
	elseif 序号==106 then
		团队副本类:团队副本创建(内容.数字id,内容)
	elseif 序号==107 then
		团队副本类:团队副本加入(内容.数字id,内容.团长id)
	elseif 序号==108 then
        团队副本类:团队副本查看(内容.数字id)
	elseif 序号==109 then
        团队副本类:团队副本加入申请(内容.数字id,内容.申请人员)
	elseif 序号==110 then
        团队副本类:团队副本加入拒绝(内容.数字id,内容.申请人员)
	elseif 序号==111 then
        团队副本类:团队副本踢出队员(内容.数字id,内容.拒绝id)
	elseif 序号==112 then
        团队副本类:团队副本退出团队(内容.数字id)
	elseif 序号==113 then
        团队副本类:团队副本解散(内容.数字id)
	elseif 序号==114 then
        团队副本类:团队副本开启(内容.数字id)
	elseif  序号 == 107.4 then
		self:发布红包传音(内容.数字id,内容)
	elseif  序号 == 107.5 then
		发送数据(玩家数据[内容.数字id].连接id,131.2,{银子=玩家数据[内容.数字id].角色.银子,仙玉=0})
	elseif  序号 == 107.6 then
		发送数据(玩家数据[内容.数字id].连接id,131.4,获取新建数据(内容.数字id,"红包记录"))
	elseif  序号 == 107.7 then
		self:领取红包传音(内容.数字id,内容)
	elseif 序号==120 then
		self:密码锁处理(内容.数字id,内容)
	elseif 序号==121 then
		self:解锁处理(内容.数字id,内容)
	elseif 序号==122 then
		self:修改处理(内容.数字id,内容)
	elseif 序号==177 then
		助战处理类:数据处理(内容.数字id, 内容.序列, 内容.文本, 内容.参数,内容.编号)
	elseif 序号==178 then
		神兵异兽榜:数据处理(内容.数字id, 内容.文本, 内容.参数,内容.编号)
	elseif 序号 == 179 then
		商会处理类:数据处理(内容.数字id, 内容.参数, 内容.序列, 内容.文本)
	elseif 序号 == 180 then
		商会处理类:上柜商品处理(内容.数字id, 内容.参数, 内容.序列, 内容.文本, tonumber(内容.编号))
	elseif 序号 == 181 then
		商会处理类:购买唤兽处理(内容.数字id, 内容.参数, 内容.序列, 内容.文本)
	elseif 序号 == 182 then
		商会处理类:购买商品处理(内容.数字id, 内容.参数, 内容.序列, 内容.文本, 内容.编号)
	elseif 序号 == 183 then
		self:黑市拍卖(内容.数字id)
	elseif 序号 == 184 then
		宝箱解密类:数据处理(内容.数字id, 内容.参数, 内容.序列, 内容.文本)
	elseif 序号 == 201 then
		玩家数据[内容.数字id].角色:加点方案报存(内容.数字id)
	elseif 序号 == 202 then
		玩家数据[内容.数字id].角色:加点方案替换(内容.方案)
	elseif 序号 == 203 then
		玩家数据[内容.数字id].角色:启用当前方案(内容.方案)
	elseif 序号 == 211 then
   	    多开系统:切换角色(内容.数字id,内容)
	elseif 序号 == 262 then
		self:战备检查处理(内容.数字id)
	elseif 序号 == 263 then
		local 发送信息 = {}
		local id =内容.数字id
		发送信息.物品数据 = {}
		发送信息.锁定附魔 = 玩家数据[id].角色.锁定附魔 or {}
		for n=1,6 do
			if 玩家数据[id].角色.装备[n]~=nil and 玩家数据[id].道具.数据[玩家数据[id].角色.装备[n]].临时效果~=nil then
				发送信息.物品数据[n] = {}
				发送信息.物品数据[n] = 玩家数据[id].道具.数据[玩家数据[id].角色.装备[n]].临时效果
			end
		end
		发送数据(玩家数据[id].连接id, 20063,发送信息 )
	elseif 序号 == 265 then
		local id =内容.数字id
		local 临时格子 =tonumber(内容.文本)
		玩家数据[id].角色.锁定附魔 = 玩家数据[id].角色.锁定附魔 or {}
		玩家数据[id].角色.锁定附魔[临时格子]=false
	elseif 序号 == 266 then
		self:一键附魔处理(内容.数字id)
	elseif 序号 == 269 then
		local id =内容.数字id
		local 临时格子 =tonumber(内容.文本)
		玩家数据[id].角色.锁定附魔 = 玩家数据[id].角色.锁定附魔 or {}
		玩家数据[id].角色.锁定附魔[临时格子]=true
	end
end
-- function 系统处理类:一键附魔处理(id)
-- 	local 附魔属性 = {"伤害","力量","命中","体质","速度","耐力","愤怒","防御","魔力","气血","法防","法术伤害","固定伤害","气血回复效果","封印命中等级","抵抗封印等级"}
-- 	local 下限表 = {
-- 	伤害 = math.floor(180 *0.5) ,
-- 	力量 = math.floor(180 *0.2),
-- 	命中 = math.floor(180 *1),
-- 	体质 = math.floor(180 *0.2),
-- 	速度 = math.floor(180 *0.2),
-- 	耐力 = math.floor(180 *0.2),
-- 	愤怒 = math.floor(180 *0.1),
-- 	防御 = math.floor(180 *0.5),
-- 	魔力 = math.floor(180 *0.2),
-- 	气血 = math.floor(180 *1),
-- 	法防 = math.floor(180 *0.2),
-- 	法术伤害 =math.floor(180 *0.2),
-- 	固定伤害 =math.floor(180 *0.2),
-- 	气血回复效果 =math.floor(180 *0.2),
-- 	封印命中等级 =math.floor(180 *0.2),
-- 	抵抗封印等级 =math.floor(180 *0.2)
-- 	}
-- 	local 上限表 = {
-- 	伤害 = math.floor(180 *1) ,
-- 	力量 = math.floor(180 *0.5),
-- 	命中 = math.floor(180 *2),
-- 	体质 = math.floor(180 *0.5),
-- 	速度 = math.floor(180 *0.5),
-- 	耐力 = math.floor(180 *0.5),
-- 	愤怒 = math.floor(180 *0.2),
-- 	防御 = math.floor(180 *1),
-- 	魔力 = math.floor(180 *0.5),
-- 	气血 = math.floor(180 *2),
-- 	法防 = math.floor(180 *0.5),
-- 	法术伤害 =math.floor(180 *0.5),
-- 	固定伤害 =math.floor(180 *0.5),
-- 	气血回复效果 =math.floor(180 *0.5),
-- 	封印命中等级 =math.floor(180 *0.5),
-- 	抵抗封印等级 =math.floor(180 *0.5)
-- 	}
-- 	玩家数据[id].角色.锁定附魔=玩家数据[id].角色.锁定附魔 or {}
-- 	if 玩家数据[id].角色.装备==nil then
-- 		发送数据(玩家数据[id].连接id, 7, "#y/一键附魔需要身上6个部位全部装备，请重试")
-- 		return
-- 	elseif 玩家数据[id].角色.装备[1]==nil or 玩家数据[id].角色.装备[2]==nil or 玩家数据[id].角色.装备[3]==nil or 玩家数据[id].角色.装备[4]==nil or 玩家数据[id].角色.装备[5]==nil or 玩家数据[id].角色.装备[6]==nil then
-- 		发送数据(玩家数据[id].连接id, 7, "#y/一键附魔需要身上6个部位全部装备，请重试")
-- 		return
-- 	elseif 玩家数据[id].角色:取道具格子1("道具")<1 then
-- 		发送数据(玩家数据[id].连接id, 7, "#y/请先在道具栏空出1个包裹")
-- 		return
-- 	end
-- 	for n=1,#附魔属性  do
-- 		if 玩家数据[id].角色.锁定附魔[n] then
-- 			if 玩家数据[id].角色.银子>=300000 then
-- 				玩家数据[id].角色:扣除银子(300000,0,0,"一键附魔",1)
-- 			else
-- 				发送数据(玩家数据[id].连接id, 7, "#y/您身上银子不足,无法附魔")
-- 				return
-- 			end
-- 			if  附魔属性[n] == "气血回复效果" or 附魔属性[n] == "伤害" or 附魔属性[n] == "防御" or 附魔属性[n] ==  "气血" then
-- 				if  玩家数据[id].角色.装备[3] then
-- 					local 道具id = 玩家数据[id].道具:卸下装备(玩家数据[id].连接id,id,{道具=3,类型="道具"})
-- 					self:添加装备临时效果(玩家数据[id].道具.数据[玩家数据[id].角色.道具[道具id]],附魔属性[n],math.random(下限表[附魔属性[n]], 上限表[附魔属性[n]]))
-- 					玩家数据[id].道具:佩戴装备(玩家数据[id].连接id,id,{道具=道具id,类型="道具"})
-- 				end
-- 			elseif  附魔属性[n] == "命中" or 附魔属性[n] == "耐力" then
-- 				if  玩家数据[id].角色.装备[1] then
-- 					local 道具id = 玩家数据[id].道具:卸下装备(玩家数据[id].连接id,id,{道具=1,类型="道具"})
-- 					self:添加装备临时效果(玩家数据[id].道具.数据[玩家数据[id].角色.道具[道具id]],附魔属性[n],math.random(下限表[附魔属性[n]], 上限表[附魔属性[n]]))
-- 					玩家数据[id].道具:佩戴装备(玩家数据[id].连接id,id,{道具=道具id,类型="道具"})
-- 				end
-- 			elseif 附魔属性[n] == "体质" or 附魔属性[n] == "力量" or 附魔属性[n] == "法术伤害"  then
-- 				if  玩家数据[id].角色.装备[2] then
-- 					local 道具id = 玩家数据[id].道具:卸下装备(玩家数据[id].连接id,id,{道具=2,类型="道具"})
-- 					self:添加装备临时效果(玩家数据[id].道具.数据[玩家数据[id].角色.道具[道具id]],附魔属性[n],math.random(下限表[附魔属性[n]], 上限表[附魔属性[n]]))
-- 					玩家数据[id].道具:佩戴装备(玩家数据[id].连接id,id,{道具=道具id,类型="道具"})
-- 				end
-- 			elseif 附魔属性[n] == "魔力" or 附魔属性[n] == "法防" then
-- 				if  玩家数据[id].角色.装备[4] then
-- 					local 道具id = 玩家数据[id].道具:卸下装备(玩家数据[id].连接id,id,{道具=4,类型="道具"})
-- 					self:添加装备临时效果(玩家数据[id].道具.数据[玩家数据[id].角色.道具[道具id]],附魔属性[n],math.random(下限表[附魔属性[n]], 上限表[附魔属性[n]]))
-- 					玩家数据[id].道具:佩戴装备(玩家数据[id].连接id,id,{道具=道具id,类型="道具"})
-- 				end
-- 			elseif 附魔属性[n] == "封印命中等级" or 附魔属性[n] == "愤怒"  then
-- 				if  玩家数据[id].角色.装备[5] then
-- 					local 道具id = 玩家数据[id].道具:卸下装备(玩家数据[id].连接id,id,{道具=5,类型="道具"})
-- 					self:添加装备临时效果(玩家数据[id].道具.数据[玩家数据[id].角色.道具[道具id]],附魔属性[n],math.random(下限表[附魔属性[n]], 上限表[附魔属性[n]]))
-- 					玩家数据[id].道具:佩戴装备(玩家数据[id].连接id,id,{道具=道具id,类型="道具"})
-- 				end
-- 			elseif 附魔属性[n] == "抵抗封印等级" or 附魔属性[n] == "固定伤害" or 附魔属性[n] == "速度" then
-- 				if  玩家数据[id].角色.装备[6] then
-- 					local 道具id = 玩家数据[id].道具:卸下装备(玩家数据[id].连接id,id,{道具=6,类型="道具"})
-- 					self:添加装备临时效果(玩家数据[id].道具.数据[玩家数据[id].角色.道具[道具id]],附魔属性[n],math.random(下限表[附魔属性[n]], 上限表[附魔属性[n]]))
-- 					玩家数据[id].道具:佩戴装备(玩家数据[id].连接id,id,{道具=道具id,类型="道具"})
-- 				end
-- 			end
-- 		end
-- 	end
-- 	local 发送信息 = {}
-- 	发送信息.物品数据 = {}
-- 	发送信息.锁定附魔 = 玩家数据[id].角色.锁定附魔 or {}
-- 	for n=1,6 do
-- 		if 玩家数据[id].角色.装备[n]~=nil and 玩家数据[id].道具.数据[玩家数据[id].角色.装备[n]].临时效果~=nil then
-- 			发送信息.物品数据[n] = {}
-- 			发送信息.物品数据[n] = 玩家数据[id].道具.数据[玩家数据[id].角色.装备[n]].临时效果
-- 		end
-- 	end
-- 	玩家数据[id].角色:刷新信息()
-- 	发送数据(玩家数据[id].连接id, 7, "#y/一键附魔成功！")
-- 	发送数据(玩家数据[id].连接id, 20064,发送信息 )
-- 	self:战备检查刷新(id)
-- end
function 系统处理类:处理帮派传送请求(内容, id)
    -- 调试输出接收到的完整数据
    print("接收到帮派传送请求, 完整数据:", table.tostring(内容))
    
    -- 通过当前请求获取正确的玩家ID
    if id == nil then
        id = __S系统.发送者
        print("未在请求中找到数字id，使用系统发送者ID:", id)
    else
        print("使用请求中的数字id:", id)
    end
    
    if not 玩家数据[id] then
        print("错误：找不到该ID的玩家数据:", id)
        return
    end
    
    local 帮派编号 = tonumber(内容.文本)
    if 帮派编号 == nil then
        常规提示(id, "#Y/帮派编号格式错误，请输入数字")
        print("帮派编号格式错误:", 内容.文本)
        return
    end
    
    print("玩家"..id.."("..玩家数据[id].角色.名称..")尝试传送到帮派"..帮派编号)
    
    if 帮派数据 == nil then
        常规提示(id, "#Y/帮派系统尚未初始化，请稍后再试")
        print("帮派数据为nil，系统未初始化")
        return
    end
    
    if 帮派数据[帮派编号] ~= nil then
        -- 计算该帮派的独立地图编号
        local 帮派地图 = 11216 + 帮派编号

        -- 确保地图已初始化
        if not 地图处理类.地图数据[帮派地图] then
            地图处理类:补充地图(帮派地图)
            地图处理类.地图坐标[帮派地图] = 地图坐标类(1217)
            -- 初始化帮派地图的NPC
            地图处理类:初始化帮派NPC(帮派地图, 帮派编号)
        end

        -- 传送玩家到对应帮派独立地图
        print("找到帮派:"..帮派编号..", 名称:"..帮派数据[帮派编号].帮派名称..", 独立地图:"..帮派地图)
        地图处理类:跳转地图(id, 帮派地图, 131, 101)
        常规提示(id, "#Y/成功传送到帮派 #G" .. 帮派数据[帮派编号].帮派名称 .. " #Y领地")
        -- 记录传送操作
        print("玩家"..id.."成功传送到帮派"..帮派编号..", 名称:"..帮派数据[帮派编号].帮派名称..", 独立地图:"..帮派地图..",坐标:131,101")
    else
        -- 列出可用的帮派编号供参考
        local 帮派列表 = ""
        local 计数 = 0
        for k, v in pairs(帮派数据) do
            帮派列表 = 帮派列表 .. "#G" .. k .. "#Y:#R" .. v.帮派名称 .. "#Y  "
            计数 = 计数 + 1
            if 计数 >= 5 then break end
        end
        if 帮派列表 ~= "" then
            常规提示(id, "#Y/未找到帮派编号 #R"..帮派编号.." #Y，请确认后重试。\n可传送的部分帮派列表: " .. 帮派列表)
        else
            常规提示(id, "#Y/未找到帮派编号 #R"..帮派编号.." #Y，请确认后重试")
        end
        print("未找到帮派编号"..帮派编号.."，可用帮派:"..帮派列表)
    end
end
function 系统处理类:战备检查刷新(id)
	if  玩家数据[id].角色.门派 =="无门派" then
		发送数据(玩家数据[id].连接id, 7, "#y/请先拜入门派在查看")
		return
	end
	self.发送信息 = {}
	self.发送信息.气血上限 = 玩家数据[id].角色.气血上限
	self.发送信息.最大气血 = 玩家数据[id].角色.最大气血
	self.发送信息.当前气血 = 玩家数据[id].角色.气血
	self.发送信息.当前魔法 = 玩家数据[id].角色.魔法
	self.发送信息.魔法上限 = 玩家数据[id].角色.最大魔法
	self.发送信息.伤害 = 玩家数据[id].角色.伤害
	self.发送信息.防御 = 玩家数据[id].角色.防御
	self.发送信息.速度 = 玩家数据[id].角色.速度
	self.发送信息.灵力 = 玩家数据[id].角色.灵力
	self.发送信息.变身 = {属性={}}
	if 玩家数据[id].角色.变身数据 and 变身卡数据[玩家数据[id].角色.变身数据] then
		self.发送信息.变身.属性.属性=变身卡数据[玩家数据[id].角色.变身数据].类型
		self.发送信息.变身.属性.数值=变身卡数据[玩家数据[id].角色.变身数据].属性
	end
	self.发送信息.宠物携带 = {
	数量 = #玩家数据[id].召唤兽.数据,
	上限 = 玩家数据[id].角色.召唤兽携带上限
	}
	self.发送信息.子女携带 = #(玩家数据[id].角色.子女列表 or {})
	self.发送信息.追加技能=玩家数据[id].角色.追加技能  or "无"
	self.发送信息.附加技能=玩家数据[id].角色.附加技能  or "无"
	self.发送信息.坐骑 = 玩家数据[id].角色.坐骑
	self.发送信息.装备数据 = 装备数据
	local 附魔属性 = {"伤害","力量","命中","体质","速度","耐力","愤怒","防御","魔力","气血","法防","法术伤害","固定伤害","气血回复效果","封印命中等级","抵抗封印等级"}
	self.发送信息.附魔数据 = 0
	for n=1,6 do
		self.发送信息[n] = {}
		self.发送信息[n] = 玩家数据[id].道具.数据[玩家数据[id].角色.装备[n]]
	end
	for i=1,#附魔属性  do
		for n=1,6 do
			if 玩家数据[id].角色.装备[n]~=nil and 玩家数据[id].道具.数据[玩家数据[id].角色.装备[n]].临时效果~=nil and 玩家数据[id].道具.数据[玩家数据[id].角色.装备[n]].临时效果[i]~=nil then
				self.发送信息.附魔数据 = self.发送信息.附魔数据 +1
			end
		end
	end
	发送数据(玩家数据[id].连接id,20065,self.发送信息)
end
function 系统处理类:添加装备临时效果(RoleItem,Type,Value)
	if RoleItem.临时效果 ==nil then
		RoleItem.临时效果={[1]= {类型 = Type,数值 =Value ,时间 = os.time() + 172800}}
	else
		for i,v in pairs(RoleItem.临时效果) do
			if v.类型 == Type then
				RoleItem.临时效果[i].数值=Value
				RoleItem.临时效果[i].时间=os.time() + 172800
				return
			end
		end
		RoleItem.临时效果[#RoleItem.临时效果+1]= {类型 = Type,数值 =Value ,时间 = os.time() + 172800}
	end
end
function 系统处理类:战备检查处理(id)
	if  玩家数据[id].角色.门派 =="无门派" then
		发送数据(玩家数据[id].连接id, 7, "#y/请先拜入门派在查看")
		return
	end
	self.发送信息 = {}
	self.发送信息.气血上限 = 玩家数据[id].角色.气血上限
	self.发送信息.最大气血 = 玩家数据[id].角色.最大气血
	self.发送信息.当前气血 = 玩家数据[id].角色.气血
	self.发送信息.当前魔法 = 玩家数据[id].角色.魔法
	self.发送信息.魔法上限 = 玩家数据[id].角色.最大魔法
	self.发送信息.伤害 = 玩家数据[id].角色.伤害
	self.发送信息.防御 = 玩家数据[id].角色.防御
	self.发送信息.速度 = 玩家数据[id].角色.速度
	self.发送信息.灵力 = 玩家数据[id].角色.灵力
	self.发送信息.变身 = {属性={}}
	if 玩家数据[id].角色.变身数据 and 变身卡数据[玩家数据[id].角色.变身数据] then
		self.发送信息.变身.属性.属性=变身卡数据[玩家数据[id].角色.变身数据].类型
		self.发送信息.变身.属性.数值=变身卡数据[玩家数据[id].角色.变身数据].属性
	end
	self.发送信息.宠物携带 = {
	数量 = #玩家数据[id].召唤兽.数据,
	上限 = 玩家数据[id].角色.召唤兽携带上限
	}
	self.发送信息.子女携带 = #(玩家数据[id].角色.子女列表 or {})
	self.发送信息.追加技能=玩家数据[id].角色.追加技能  or "无"
	self.发送信息.附加技能=玩家数据[id].角色.附加技能  or "无"
	self.发送信息.坐骑 = 玩家数据[id].角色.坐骑
	self.发送信息.装备数据 = 装备数据
	local 附魔属性 = {"伤害","力量","命中","体质","速度","耐力","愤怒","防御","魔力","气血","法防","法术伤害","固定伤害","气血回复效果","封印命中等级","抵抗封印等级"}
	self.发送信息.附魔数据 = 0
	for n=1,6 do
		self.发送信息[n] = {}
		self.发送信息[n] = 玩家数据[id].道具.数据[玩家数据[id].角色.装备[n]]
	end
	for i=1,#附魔属性  do
		for n=1,6 do
			if 玩家数据[id].角色.装备[n]~=nil and 玩家数据[id].道具.数据[玩家数据[id].角色.装备[n]].临时效果~=nil and 玩家数据[id].道具.数据[玩家数据[id].角色.装备[n]].临时效果[i]~=nil then
				self.发送信息.附魔数据 = self.发送信息.附魔数据 +1
			end
		end
	end
	发送数据(玩家数据[id].连接id,20062,self.发送信息)
end
function 系统处理类:购买戏票(id,内容)
	local 名称=内容.项目
	local 价格=200
	if 名称=="一斛珠" then
		价格=150
		if 调试模式 then
			价格=0
		end
		if 玩家数据[id].角色:扣除积分(价格,"副本积分") then
			玩家数据[id].道具:给予道具(id,"癫散戏票·一斛珠")
			常规提示(id,"#Y购买#G癫散戏票·一斛珠#Y成功。")
		end
	elseif 名称=="五更寒" then
		if 调试模式 then
			价格=0
		end
		if 玩家数据[id].角色:扣除积分(价格,"副本积分") then
			玩家数据[id].道具:给予道具(id,"癫散戏票·五更寒")
			常规提示(id,"#Y购买#G癫散戏票·五更寒#Y成功。")
		end
	elseif 名称=="双城记" then
		if 调试模式 then
			价格=0
		end
		if 玩家数据[id].角色:扣除积分(价格,"副本积分") then
			玩家数据[id].道具:给予道具(id,"癫散戏票·双城记")
			常规提示(id,"#Y购买#G癫散戏票·双城记#Y成功。")
		end
	elseif 名称=="无相菩提" then
		常规提示(id,"这个戏票目前一票难求，请先选择其他的戏票吧。")
		return
	end
end
function 系统处理类:如梦奇谭开启(id,内容)
	local 选择=内容.类型
	local 临时={
	一斛珠="癫散戏票·一斛珠",
	五更寒="癫散戏票·五更寒",
	双城记="癫散戏票·双城记",
	}
	添加对话(id,"癫散班主","酒仙","准备好了吗？这便开启如梦奇谭"..临时[选择].."！",{"正等着这一刻哪！（"..选择.."）","还是算了吧"})
end
function 系统处理类:打字机回调(id,内容)
	local 类型=内容.类型
	if 类型 == "一斛珠" then
	elseif 类型 == "五更寒序" then
		local 副本id = 任务处理类:取副本id(id,670)
		if 副本id == 0 or 副本id ~= id  then
			return
		end
	elseif 类型 == "五更寒渔村" then
		local 副本id = 任务处理类:取副本id(id,670)
		if 副本id == 0 or 副本id ~= id  then
			return
		end
	elseif 类型 == "五更寒魔化村民赌徒" then
		local 副本id = 任务处理类:取副本id(id,670)
		if 副本id == 0 or 副本id ~= id  then
			return
		end
		if 副本数据.如梦奇谭之五更寒.进行[副本id].进程==9 and  副本数据.如梦奇谭之五更寒.进行[副本id].阶段== 0 then
			副本数据.如梦奇谭之五更寒.进行[副本id].阶段 = 1
			刷新任务670(id)
		end
	elseif 类型 == "五更寒魔化村民夜叉" then
		local 副本id = 任务处理类:取副本id(id,670)
		if 副本id == 0 or 副本id ~= id  then
			return
		end
		if 副本数据.如梦奇谭之五更寒.进行[副本id].进程==9 and  副本数据.如梦奇谭之五更寒.进行[副本id].阶段== 3 then
			副本数据.如梦奇谭之五更寒.进行[副本id].进程 = 10
			刷新队伍任务追踪(id)
			刷新任务670(id)
		end
	elseif 类型 =="五更寒结束" then
		发送数据(玩家数据[id].连接id,6560,false)
		玩家数据[id].角色:取消任务(玩家数据[id].角色:取任务(670))
		地图处理类:跳转地图(id,1501,24,16)
	elseif 类型 == "齐天大圣序" then
		local 副本id = 任务处理类:取副本id(id,581)
		if 副本id == 0 or 副本id ~= id  then
			return
		end
	elseif 类型 == "无底洞女子对话" then
		local 副本id = 任务处理类:取副本id(id,906)
		if 副本id == 0 or 副本id ~= id  then
			return
		end
	end
end
function 系统处理类:第二场景回调(数字id,内容)
	local 类型=内容.类型
	local 队长=玩家数据[数字id].队伍
	if 类型[1]==100 and 类型[2]==1 then
		if 数字id==队长 then
			地图处理类:跳转地图(数字id,7001,36,31)
		end
	elseif 类型[1]==100 and 类型[2]==2 then
		if 数字id==队长 then
			地图处理类:跳转地图(数字id,7005,11,33)
		end
		发送数据(玩家数据[数字id].连接id,6558,{调用={100,3}})
	elseif 类型[1]==100 and 类型[2]==4 then
	elseif 类型[1]==100 and 类型[2]==6 then
		local 副本id = 任务处理类:取副本id(数字id,670)
		if 副本id == 0 or 副本id ~= 数字id  then
			return
		end
		if 副本数据.如梦奇谭之五更寒.进行[副本id].进程==11 and  副本数据.如梦奇谭之五更寒.进行[副本id].阶段== 0 then
			副本数据.如梦奇谭之五更寒.进行[副本id].阶段 = 1
			刷新任务670(数字id)
		end
	elseif 类型[1]==100 and 类型[2]==3 then
		local text1={"不知过了多久……"}
		发送数据(玩家数据[数字id].连接id,6557,{文本=text1,横排显示=true,类型="五更寒动画4",动画调用={100,4},字体=nil,音乐=nil,背景=nil})
	end
end
function 系统处理类:召唤兽携带上限(数字id)
	if 玩家数据[数字id].角色.召唤兽携带上限 < 10 then
		local 数额 = 10000000
		if 玩家数据[数字id].角色.召唤兽携带上限 == 8 then
			数额 = 30000000
		elseif 玩家数据[数字id].角色.召唤兽携带上限 == 9 then
			数额 = 60000000
		elseif 玩家数据[数字id].角色.召唤兽携带上限 == 10 then
			数额 = 100000000
		end
		if 玩家数据[数字id].角色:扣除银子(数额,0,0,"召唤兽携带上限",1)  then
			玩家数据[数字id].角色.召唤兽携带上限 = 玩家数据[数字id].角色.召唤兽携带上限 +1
			发送数据(玩家数据[数字id].连接id,31,玩家数据[数字id].角色:取总数据1())
			常规提示(数字id,"#Y你的召唤兽携带上限增加至#G"..玩家数据[数字id].角色.召唤兽携带上限.."个。")
		end
	else
		常规提示(数字id,"#Y你的召唤兽携带上限到达了最大无法在扩充")
	end
end
function 系统处理类:断开游戏(数字id)
	if 玩家数据[数字id]~=nil then
		if 玩家数据[数字id].摊位数据~=nil then
			玩家数据[数字id].离线摆摊=true
			return
		elseif 玩家数据[数字id].角色.地图数据.编号==5005 and 初始活动.昆仑仙境[数字id] and 初始活动.昆仑仙境[数字id].次数 and 初始活动.昆仑仙境[数字id].次数 < 初始活动.昆仑仙境[数字id].最大次数 then
			玩家数据[数字id].离线摆摊=true
			return
    elseif 玩家数据[数字id].角色.地图数据.编号==5005 and 初始活动.昆仑仙境[数字id] and 初始活动.昆仑仙境[数字id].次数 and 初始活动.昆仑仙境[数字id].次数 < 初始活动.昆仑仙境[数字id].最大次数 then
        print("角色处于昆仑仙境活动中，设置为离线摆摊: ID = " .. 数字id)
        玩家数据[数字id].离线摆摊=true
        return
	  elseif 玩家数据[数字id].角色 and 玩家数据[数字id].角色.观看召唤兽 then
	      -- 获取召唤兽编号
	      local 编号 = 玩家数据[数字id].召唤兽:取编号(玩家数据[数字id].角色.观看召唤兽)
	      if 编号 > 0 and 玩家数据[数字id].召唤兽.数据[编号] then
	          -- 清除召唤兽的观看状态
	          玩家数据[数字id].召唤兽.数据[编号].观看 = nil
	      end

	      -- 清除玩家的观看召唤兽状态
	      玩家数据[数字id].角色.观看召唤兽 = nil

	      -- 通知客户端取消观看召唤兽（如果连接还存在）
	      if 玩家数据[数字id].连接id then
	          发送数据(玩家数据[数字id].连接id, 1040, nil)
	      end
	  end
	end
	local 是否切换角色 = false
  local 调用栈 = debug.traceback()
  if 调用栈:find("助战处理类:更换操作主角") then
      是否切换角色 = true
     -- print("检测到是由切换角色操作触发的断开游戏，将保留聊天框内容")
  end

  -- 如果是切换角色操作，通知客户端保存聊天记录
  if 是否切换角色 and 玩家数据[数字id].连接id then
      发送数据(玩家数据[数字id].连接id, 420010, {操作 = "保存聊天记录"})
  end
	if 玩家数据[数字id]~=nil and 玩家数据[数字id].战斗~=0 then
		if 战斗准备类.战斗盒子[玩家数据[数字id].战斗]~=nil then
				战斗准备类.战斗盒子[玩家数据[数字id].战斗]:设置断线玩家(数字id)
				return
		else
			玩家数据[数字id].战斗=0
		end
	end
	if 玩家数据[数字id]~=nil then
		if 玩家数据[数字id].交易信息~=nil then
			玩家数据[数字id].道具:取消交易(数字id)
		end
		if 玩家数据[数字id].队伍~=0 then
			队伍处理类:退出队伍(数字id)
		end
		玩家数据[数字id].角色.上次退出 = os.time() --记录一个下线时间，方便清除垃圾号
		--帮派处理
		if 玩家数据[数字id].角色.BPMC ~= "无帮派" then
			local bh = 玩家数据[数字id].角色.BPBH
			if 取所有帮派[bh] and 取所有帮派[bh].已解散==nil and 帮派数据[bh].成员数据[数字id] then
				if 帮派数据[bh].成员数据[数字id].职务 == "商人" then
					帮派数据[bh].成员数据[数字id].职务 =  "帮众"
					玩家数据[数字id].角色:删除称谓(帮派数据[bh].帮派名称.."商人")
					玩家数据[数字id].角色:添加称谓(帮派数据[bh].帮派名称.."帮众")
				end
				帮派数据[bh].成员数据[数字id].在线 = false
				帮派数据[bh].成员数据[数字id].离线时间 = os.time()
			end
		end
	    -- 处理团队副本状态
	    if 团队副本数据 ~= nil then
	        for i=1,#团队副本数据 do
	            if 团队副本数据[i].在团人员.状态=="在线" and 团队副本数据[i].申请人员.状态=="在线" then
	                团队副本数据[i].在团人员.状态=""
	                团队副本数据[i].申请人员.状态=""
	            end
	        end
	    end
		-- 玩家数据[数字id].角色:清空临时包裹()
		self:清除断开数据(数字id)

		-- 保存玩家个人数据
		玩家数据[数字id].角色:存档() --测试模式

		-- 保存任务数据（确保玩家下线时任务数据也被保存）
		保存任务数据()

		地图处理类:移除玩家(数字id)
		-- __S服务:输出(string.format('玩家(%s)退出游戏丨ID:%s丨账号:%s丨IP:%s',玩家数据[数字id].角色.名称,数字id,玩家数据[数字id].账号,玩家数据[数字id].ip))
		玩家数据[数字id]=nil
	end
end
function 系统处理类:加载进入信息(数字id,ip)
	帮派PK:进入游戏判断(数字id)
end
function 系统处理类:清除进入数据(数字id)
	-- 清除全局临时数据
	神秘宝箱[数字id]=nil
	抽奖转盘[数字id]=nil

	-- 清除玩家基础临时数据
	玩家数据[数字id].文墨对话=nil
	玩家数据[数字id].铃铛答题=nil
	玩家数据[数字id].答题间隔=nil
	玩家数据[数字id].最后操作=nil
	玩家数据[数字id].离线摆摊=nil
	玩家数据[数字id].宝宝内丹序号=nil
	玩家数据[数字id].物品序号=nil
	玩家数据[数字id].地图单位=nil
	玩家数据[数字id].转换坐骑=nil
	玩家数据[数字id].临时对话={}
	玩家数据[数字id].移动数据={}
	玩家数据[数字id].道具操作={}

	-- 清除助战相关临时数据
	if 玩家数据[数字id].助战识别 then
		玩家数据[数字id].助战识别=nil
	end

	-- 清除角色临时状态
	if 玩家数据[数字id].角色.临时属性 then
		玩家数据[数字id].角色.临时属性=nil
	end

	-- 清除召唤兽观看状态
	if 玩家数据[数字id].角色.观看召唤兽 then
		-- 获取召唤兽编号
		local 编号 = 玩家数据[数字id].召唤兽:取编号(玩家数据[数字id].角色.观看召唤兽)
		if 编号 > 0 and 玩家数据[数字id].召唤兽.数据[编号] then
			-- 清除召唤兽的观看状态
			玩家数据[数字id].召唤兽.数据[编号].观看 = nil
		end
		-- 清除玩家的观看召唤兽状态
		玩家数据[数字id].角色.观看召唤兽 = nil
	end

	-- 清除特殊状态
	if 玩家数据[数字id].勾魂索中 then
		玩家数据[数字id].角色:勾魂索死亡处理()
		玩家数据[数字id].勾魂索中=nil
	end
	if 玩家数据[数字id].坐牢中 then
		发送数据(玩家数据[数字id].连接id,3711)
	end
end
function 系统处理类:清除断开数据(数字id)
	玩家数据[数字id].角色.飞行中=nil
end
function 系统处理类:取单个离线玩家(账号,id)
end
function 系统处理类:离线玩家存档(账号,id,sj)
end
function 系统处理类:染色方案重置(模型)
	local lssj
	if 模型 == "巫蛮儿" then
		lssj = 201
	elseif 模型 == "杀破狼" then
		lssj = 202
	elseif 模型 == "羽灵神" then
		lssj = 203
	elseif 模型 == "桃夭夭" then
		lssj = 204
	elseif 模型 == "偃无师" then
		lssj = 205
	elseif 模型 == "鬼潇潇" then
		lssj = 206
	end
	return lssj
end
function 系统处理类:检查装备(数字id,连接id)
	local 发送 = false
	for n, v in pairs(玩家数据[数字id].角色.装备) do
		local 格子 = 玩家数据[数字id].角色.装备[n]
		if 玩家数据[数字id].道具.数据 and 玩家数据[数字id].道具.数据[格子] ~= nil then
			if 玩家数据[数字id].道具.数据[格子].临时附魔 ~= nil  then
				for k,v in pairs(玩家数据[数字id].道具.数据[格子].临时附魔) do
					if v.数值 and v.时间 < os.time() then
						玩家数据[数字id].角色:清除装备附魔属性(玩家数据[数字id].道具.数据[格子],玩家数据[数字id].道具.数据[格子].分类,k,v.数值)
						玩家数据[数字id].道具.数据[格子].临时附魔[k] = nil
						常规提示(数字id,"#Y你装备上的附魔特效消失了！")
					end
				end
			end
			if 玩家数据[数字id].道具.数据[格子].耐久 and 玩家数据[数字id].道具.数据[格子].耐久 <= 0 then
				玩家数据[数字id].道具.数据[格子].耐久=0
				local 道具格子=玩家数据[数字id].角色:取道具格子1("道具")
				if 道具格子~=0 and 玩家数据[数字id].道具.数据[格子] and 玩家数据[数字id].道具.数据[格子].分类 and 玩家数据[数字id].道具.数据[格子].分类<=6 then
					玩家数据[数字id].角色:卸下装备(玩家数据[数字id].道具.数据[格子],玩家数据[数字id].道具.数据[格子].分类)
					玩家数据[数字id].角色.装备[n]=nil
					玩家数据[数字id].角色["道具"][道具格子]=格子
					玩家数据[数字id].角色:刷新信息()
					玩家数据[数字id].道具:刷新道具行囊(数字id,"道具")
					发送数据(玩家数据[数字id].连接id,3503,玩家数据[数字id].角色:取装备数据())
					if n==3 then
						发送数据(玩家数据[数字id].连接id,3505)
						地图处理类:更新武器(数字id)
					end
					发送数据(玩家数据[数字id].连接id,12)
				end
				常规提示(数字id,"#Y你的装备耐久度不足请及时修理！")
			end
		end
	end
end
function 系统处理类:进入事件(id,连接id,不显示IP)
	发送数据(连接id,27,{文本="#R您上次退出的时间"..时间转换(玩家数据[id].角色.上次退出),频道=""})
	-- 只在正常登录时显示IP信息，更换操作主角时不显示
	if not 不显示IP and 玩家数据[id].角色.ip then
		发送数据(连接id,27,{文本="#R您上次连接ip为"..玩家数据[id].角色.ip,频道=""})
	end

	-- 检查是否有寄存物品，并发送提示消息
	if 寄存数据[id] ~= nil and #寄存数据[id] > 0 then
		local 银子数量 = 0
		local 物品数量 = 0
		local 召唤兽数量 = 0

		-- 统计各类型寄存物品的数量
		for i = 1, #寄存数据[id] do
			if 寄存数据[id][i].类型 == "银子" then
				银子数量 = 银子数量 + 1
			elseif 寄存数据[id][i].类型 == "物品" then
				物品数量 = 物品数量 + 1
			elseif 寄存数据[id][i].类型 == "召唤兽" then
				召唤兽数量 = 召唤兽数量 + 1
			end
		end

		-- 发送提示消息
		if 银子数量 > 0 then
			发送系统消息(id, "#Y/你有"..银子数量.."笔藏宝阁出售收益在贸易运输队主管处等待领取")
		end

		if 物品数量 > 0 or 召唤兽数量 > 0 then
			local 提示 = "#Y/你还有"
			if 物品数量 > 0 then
				提示 = 提示 .. 物品数量 .. "件物品"
				if 召唤兽数量 > 0 then
					提示 = 提示 .. "和"
				end
			end

			if 召唤兽数量 > 0 then
				提示 = 提示 .. 召唤兽数量 .. "只召唤兽"
			end

			提示 = 提示 .. "在贸易运输队主管处寄存，请及时领取"
			发送系统消息(id, 提示)
		end
	end

	if 玩家数据[id].角色.银子>100000000*50 then
		玩家数据[id].角色.银子=玩家数据[id].角色.银子-100000000*50
		玩家数据[id].角色.存银=玩家数据[id].角色.存银+玩家数据[id].角色.银子
		玩家数据[id].角色.银子=100000000*50
		发送系统消息(id,"#W/您身上的银子已经超过上限，多余的银两已经自动转化为存银。")
	end
	if 玩家数据[id].角色.存银>100000000*50 then
		玩家数据[id].角色.存银=100000000*50
		发送系统消息(id,"#W/由于您的存银突破系统上限，多余的部分自动捐赠大唐金库用作国家发展，谢谢您的大力支持。")
	end
	if 玩家数据[id].角色.帮派玄武==nil then
		玩家数据[id].角色.帮派玄武={次数=0}
	end
	if 玩家数据[id].角色.钓鱼积分==nil then
		玩家数据[id].角色.钓鱼积分=0
	end
	if 玩家数据[id].角色.小龟积分==nil then
		玩家数据[id].角色.小龟积分=0
	end
	if 玩家数据[id].角色.秘制积分==nil then
		玩家数据[id].角色.秘制积分=0
	end
	if 玩家数据[id].角色.祈福==nil then
		玩家数据[id].角色.祈福=0
	end
	if 体验状态开关 and f函数.读配置(程序目录..[[data\]]..玩家数据[id].账号..[[\账号信息.txt]],"账号配置","体验状态") == "0" then
		玩家数据[id].角色.体验状态=true
		发送数据(连接id,224)
	end
	self.找到节日=false
	for n=1,52 do
		self.当前时间=os.date("%m月%d日")
		self.节日时间=f函数.读配置(程序目录.."游戏设置/节日设置.ini","节日时间","节日时间"..n)
		self.节日名称=f函数.读配置(程序目录.."游戏设置/节日设置.ini","节日名称","节日名称"..n)
		if self.当前时间==self.节日时间 then
			发送数据(连接id,1501,{名称="节日礼物使者",模型="兔子怪",对话=format("今天是#R/"..self.节日名称.."#W/，请前往长安节日礼物使者#Y/（208，152）#W/处领取节日礼物，祝您天天有个好心情！#77")})
			self.找到节日=true
			break
		end
	end
	if self.找到节日 then
		节日开关=true
	else
		节日开关=false
	end


	for n=1,#玩家数据[id].角色.好友数据.好友 do
		local 好友id=玩家数据[id].角色.好友数据.好友[n].id
		if 玩家数据[好友id]~=nil and 玩家数据[id].角色.好友数据.好友[n].好友度~=nil and 玩家数据[id].角色.好友数据.好友[n].好友度>=10 then
			for i=1,#玩家数据[好友id].角色.好友数据.好友 do
				if 玩家数据[好友id].角色.好友数据.好友[i].id==id and 玩家数据[好友id].角色.好友数据.好友[i].好友度~=nil and 玩家数据[好友id].角色.好友数据.好友[i].好友度>=10 then
					发送数据(玩家数据[好友id].连接id,38,{内容="#W你的好友#Y"..玩家数据[id].角色.名称.."#W进入了游戏",频道="xt"})
				end
			end
		end
	end
	if 玩家数据[id].角色.名称 ~="游戏管理员" and 玩家数据[id].角色.ID < 400000 then
		-- 确保排行数据结构完整
		if not 排行数据 then
			排行数据 = {}
		end
		if not 排行数据.属性排行 then
			排行数据.属性排行 = {
				气血排行 = {},
				防御排行 = {},
				伤害排行 = {},
				速度排行 = {},
				命中排行 = {},
				灵力排行 = {},
				魔法排行 = {},
				躲避排行 = {}
			}
		end
		if not 排行数据.数据排行 then
			排行数据.数据排行 = {
				银子排行 = {},
				储备排行 = {},
				存银排行 = {},
				人气排行 = {},
				帮贡排行 = {},
				在线时间 = {},
				人物等级 = {},
				活跃排行 = {}
			}
		end

		local 属性 = {"最大气血","防御","伤害","速度","命中","灵力","最大魔法","躲避"}
		local 排行属性 ={"气血排行","防御排行","伤害排行","速度排行","命中排行","灵力排行","魔法排行","躲避排行"}
		for o=1,8 do
			-- 确保每个排行属性都存在
			if not 排行数据.属性排行[排行属性[o]] then
				排行数据.属性排行[排行属性[o]] = {}
			end
			for i=1,20 do
				if 排行数据.属性排行[排行属性[o]][i] == nil then
					排行数据.属性排行[排行属性[o]][i] = {名称=玩家数据[id].角色.名称,数值=玩家数据[id].角色[属性[o]]}
					break
				elseif 玩家数据[id].角色.名称 == 排行数据.属性排行[排行属性[o]][i].名称 then
					排行数据.属性排行[排行属性[o]][i].数值=玩家数据[id].角色[属性[o]]
					break
				elseif 玩家数据[id].角色[属性[o]] > 排行数据.属性排行[排行属性[o]][i].数值 then
					table.remove(排行数据.属性排行[排行属性[o]],20)
					table.insert(排行数据.属性排行[排行属性[o]],{名称=玩家数据[id].角色.名称,数值=玩家数据[id].角色[属性[o]]})
					break
				end
			end
			if #排行数据.属性排行[排行属性[o]] > 1 then
				删除重复组(排行数据.属性排行[排行属性[o]])
			end
			table.sort(排行数据.属性排行[排行属性[o]], function (a,b) return a.数值>b.数值 end )
		end
		local 属性 = {"门贡","知了积分","长安保卫战累计积分","活动积分","比武积分","副本积分","成就积分","单人积分"}
		local 排行属性 ={"门贡排行","知了积分","长安保卫","活动积分","比武积分","副本积分","成就积分","单人积分"}
		local 属性 = {"银子","储备","存银","人气","BG","在线时间","等级","活跃度"}
		local 排行属性 ={"银子排行","储备排行","存银排行","人气排行","帮贡排行","在线时间","人物等级","活跃排行"}
		for o=1,8 do
			-- 确保每个数据排行属性都存在
			if not 排行数据.数据排行[排行属性[o]] then
				排行数据.数据排行[排行属性[o]] = {}
			end
		    for i=1,20 do
		        if 排行数据.数据排行[排行属性[o]][i] == nil then
		            local 数值 = 玩家数据[id].角色[属性[o]]
		            if o == 1 then
		                数值 = 玩家数据[id].角色[属性[o]] + 玩家数据[id].角色.银子
		                if 数值 > 800000000 then 数值 = 800000000 end -- 限制银子排行
		            elseif o == 2 then
		                if 数值 > 800000000 then 数值 = 800000000 end -- 限制储备排行
		            elseif o == 6 then
		                数值 = 玩家数据[id].角色[属性[o]].小时
		            end
		            排行数据.数据排行[排行属性[o]][i] = {名称=玩家数据[id].角色.名称, 数值=数值}
		            break
		        elseif 玩家数据[id].角色.名称 == 排行数据.数据排行[排行属性[o]][i].名称 then
		            local 数值 = 玩家数据[id].角色[属性[o]]
		            if o == 1 then
		                数值 = 玩家数据[id].角色[属性[o]] + 玩家数据[id].角色.银子
		                if 数值 > 800000000 then 数值 = 800000000 end -- 限制银子排行
		            elseif o == 2 then
		                if 数值 > 800000000 then 数值 = 800000000 end -- 限制储备排行
		            elseif o == 6 then
		                数值 = 玩家数据[id].角色[属性[o]].小时
		            end
		            排行数据.数据排行[排行属性[o]][i].数值 = 数值
		            break
		        elseif o == 1 then
		            local 数值 = 玩家数据[id].角色[属性[o]] + 玩家数据[id].角色.银子
		            if 数值 > 800000000 then 数值 = 800000000 end -- 限制银子排行
		            if 数值 > 排行数据.数据排行[排行属性[o]][i].数值 then
		                table.remove(排行数据.数据排行[排行属性[o]], 20)
		                table.insert(排行数据.数据排行[排行属性[o]], {名称=玩家数据[id].角色.名称, 数值=数值})
		                break
		            end
		        elseif o == 2 then
		            local 数值 = 玩家数据[id].角色[属性[o]]
		            if 数值 > 800000000 then 数值 = 800000000 end -- 限制储备排行
		            if 数值 > 排行数据.数据排行[排行属性[o]][i].数值 then
		                table.remove(排行数据.数据排行[排行属性[o]], 20)
		                table.insert(排行数据.数据排行[排行属性[o]], {名称=玩家数据[id].角色.名称, 数值=数值})
		                break
		            end
		        elseif o == 6 then
		            local 数值 = 玩家数据[id].角色.在线时间.小时
		            if 数值 > 排行数据.数据排行[排行属性[o]][i].数值 then
		                table.remove(排行数据.数据排行[排行属性[o]], 20)
		                table.insert(排行数据.数据排行[排行属性[o]], {名称=玩家数据[id].角色.名称, 数值=数值})
		                break
		            end
		        else
		            local 数值 = 玩家数据[id].角色[属性[o]]
		            if 数值 > 排行数据.数据排行[排行属性[o]][i].数值 then
		                table.remove(排行数据.数据排行[排行属性[o]], 20)
		                table.insert(排行数据.数据排行[排行属性[o]], {名称=玩家数据[id].角色.名称, 数值=数值})
		                break
		            end
		        end
		    end
		    if #排行数据.数据排行[排行属性[o]] > 1 then
		        删除重复组(排行数据.数据排行[排行属性[o]])
		    end
		    table.sort(排行数据.数据排行[排行属性[o]], function(a, b) return a.数值 > b.数值 end)
		end
	end
end
function 系统处理类:创建账号(id,序号,内容)
	if 内容.ip~=nil and f函数.读配置(程序目录 .. "游戏记录/ip封禁.ini", "ip", 内容.ip)=="1" then
		__S服务:输出(string.format("封禁ip的客户进入试图进入(%s):%s:%s", 内容.id, 内容.ip,id))
		发送数据(id,7,"#R该IP地址已在服务器黑名单内，无法建立连接！")
		return 0
	end
	local 账号数量=1
	if 内容.ip~=nil and 内容.硬盘~=nil then
		if f函数.读配置(程序目录 .. "游戏记录/账号数量.ini", "ip", 内容.ip)~="空" then
			账号数量=f函数.读配置(程序目录 .. "游戏记录/账号数量.ini", "ip", 内容.ip)+1
		end
	else
		发送数据(id,7,"#R数据异常，请与管理员联系！")
		__S服务:断开连接(id)
		return
	end
	if f函数.文件是否存在(程序目录..[[data/]]..内容.账号)==false then
		os.execute("md "..[[data\]]..内容.账号)
		local file =io.open([[data\]]..内容.账号..[[\信息.txt]],"w")
		file:write([[do local ret={} return ret end]])
		file:close()
		local file =io.open([[data\]]..内容.账号..[[\账号信息.txt]],"r")
		f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","密码",内容.密码)
		f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","管理","0")
		f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","安全码","0")
		f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","创建时间",时间转换(os.time()))
		f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","创建ip",内容.ip)
		f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","点卡","0")
		f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","体验状态","0")
		写配置("./游戏记录/账号数量.ini","ip",内容.ip,账号数量)
		发送数据(id,3.1)
		发送数据(id,7,"#G账号注册成功！！！")
	else
		发送数据(id,7,"#R该账户已被注册！")
	end
end
function 系统处理类:账号验证(id,序号,内容)
	if 内容.ip~=nil and f函数.读配置(程序目录 .. "游戏记录/ip封禁.ini", "ip", 内容.ip)=="1" then
		__S服务:输出(string.format("封禁ip的客户进入试图进入(%s):%s:%s", 内容.id, 内容.ip,id))
		发送数据(id,7,"#R该IP地址已在服务器黑名单内，无法建立连接！")
		return 0
	elseif f函数.读配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","封禁")=="1" then
		发送数据(id,7,"#R该账号已经被禁止登录游戏")
		return
	elseif f函数.读配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","时间封禁")~="空" then
		local sj=f函数.读配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","时间封禁")+0
		if os.time()-sj<0 then
			发送数据(id,7,"#R该账号被限制于"..时间转换(sj).."后才能登录")
			return
		else
			f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","时间封禁","空")
		end
	end
	if f函数.文件是否存在(程序目录..[[data/]]..内容.账号)==false then
		发送数据(id,7,"#Y该账户未被注册！")
	else
		local 密码=f函数.读配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","密码")
		if 密码~=内容.密码 then
			发送数据(id,7,"#Y您输入的密码不正确！")
			return
		end
		self:取角色选择信息(id,内容.账号)
	end
end
function 系统处理类:取角色选择信息(id,账号)
	self.临时文件=读入文件(程序目录..[[data/]]..账号..[[/信息.txt]])
	self.写入信息=table.loadstring(self.临时文件)
	self.发送数据={}
	for n=1,#self.写入信息 do
		local 武器数据={名称="",级别限制=0,子类=0}
		self.读取文件=读入文件(程序目录..[[data/]]..账号..[[/]]..self.写入信息[n]..[[/角色.txt]])
		self.还原数据=table.loadstring(self.读取文件)
		if self.还原数据.装备[3]~=nil or self.还原数据.锦衣~=nil then
			local 临时文件=读入文件(程序目录..[[data/]]..账号..[[/]]..self.写入信息[n]..[[/道具.txt]])
			local 临时道具=table.loadstring(临时文件)
			if self.还原数据.装备[3]~=nil and 临时道具 then
				武器数据=临时道具[self.还原数据.装备[3]]
			end
			self.发送数据[#self.发送数据+1]={名称=self.还原数据.名称,等级=self.还原数据.等级,炫彩=self.还原数据.炫彩,炫彩组=self.还原数据.炫彩组,染色方案=self.还原数据.染色方案,染色组=self.还原数据.染色组,造型=self.还原数据.造型,武器数据=武器数据,门派=self.还原数据.门派,id=self.还原数据.数字id,历劫=self.还原数据.历劫}
		end
	end
	发送数据(id,4,self.发送数据)
	self.发送数据={}
	self.读取文件=nil
	self.还原数据=nil
	self.临时文件=nil
	self.写入信息=nil
end

function 系统处理类:设置传说物品(id)
  --修炼
  local 任务id = (玩家数据[id].角色:取任务(13) ~= 0) and 玩家数据[id].角色:取任务(13) or 玩家数据[id].角色:取任务(501)
  if 任务id~=0 and 任务数据[任务id]~=nil and 任务数据[任务id].传说==nil and 任务数据[任务id].分类 ~= 15 and 任务数据[任务id].分类 ~= 11 then
    任务数据[任务id].传说=1
    if 任务数据[任务id].等级==nil then
      玩家数据[id].道具:给予道具(id,任务数据[任务id].物品,1,30)
      常规提示(id,"#Y你获得了传说中的#R"..任务数据[任务id].物品)
      发送数据(玩家数据[id].连接id,38,{内容="你得到了#R/"..任务数据[任务id].物品})
    else
      取传说装备(id,任务数据[任务id].等级,任务数据[任务id].物品)
      常规提示(id,"#Y你获得了传说中的#R"..任务数据[任务id].物品)
      发送数据(玩家数据[id].连接id,38,{内容="你得到了#R/"..任务数据[任务id].物品})
    end
  end
end
function 系统处理类:显示(x,y)
end
function 系统处理类:进入游戏(id,账号,数字id,ip)
	if id =="假人" then id =888888 end
	数字id=数字id+0
	LianjieID[id]=true
	if 玩家数据[数字id]~=nil then
		if 玩家数据[数字id].交易信息~=nil then
			玩家数据[数字id].道具:取消交易(数字id)
		end
		发送数据(玩家数据[数字id].连接id,998,"您的账号已经从另一台电脑上登录，如非本人操作，请立即修改游戏密码")
		玩家数据[数字id].连接id=id
		玩家数据[数字id].助战=nil
		玩家数据[数字id].角色.连接id=id
		if 玩家数据[数字id].角色.地图数据.编号 >=100000 and 玩家数据[数字id].角色.地图数据.编号 <=40000000 then
	     -- local x = tonumber(string.sub(玩家数据[数字id].角色.地图数据.编号,1,7))
	      for i=1,#房屋数据 do
	      	if 房屋数据[i].ID == x then
	        发送数据(id,119.2,房屋数据[i])
	      	end
	      end
	    end
		发送数据(id,5,玩家数据[数字id].角色:取总数据())
		玩家数据[数字id].道具:索要道具3(id,数字id)
		发送数据(id,16,玩家数据[数字id].召唤兽.数据)
		发送数据(id,25,宝宝队伍图)
		发送数据(id,997,{id=数字id,用户="正式用户",名称=玩家数据[数字id].角色.名称,账号=玩家数据[数字id].账号})
		发送数据(id,43,时辰信息.昼夜)
		发送数据(id,433,时辰信息.天气)
		发送数据(id,96.1,玩家数据[数字id].孩子.数据)
		发送数据(id,11,时辰信息.当前)
		-- 清理战斗自动栏数据
		发送数据(id,5505.5,{})

		-- 如果在战斗中，需要清除战斗实例中的助战关系（处理顶号情况）
		if 玩家数据[数字id].战斗 ~= 0 and 战斗准备类.战斗盒子[玩家数据[数字id].战斗] then
			local 战斗实例 = 战斗准备类.战斗盒子[玩家数据[数字id].战斗]
			-- 清除该玩家在战斗实例中的助战关系
			for k, v in pairs(战斗实例.参战玩家) do
				if v.id == 数字id then
					-- 普通登录后，清除助战关系
					v.对接id = nil
					v.连接id = 玩家数据[数字id].连接id
					break
				end
			end
			-- 强制刷新自动数据
			战斗实例:刷新自动数据()
		end

		玩家数据[数字id].角色:刷新任务跟踪()
		商城处理类:取商品组数据(数字id, 18)
		地图处理类:重连加入(数字id,玩家数据[数字id].角色.地图数据.编号,玩家数据[数字id].角色.地图数据.x,玩家数据[数字id].角色.地图数据.y)

		if 玩家数据[数字id].摊位数据~=nil then
			玩家数据[数字id].道具:收摊处理(玩家数据[数字id].连接id,数字id)
		end
		if 团队副本数据 ~= nil then
			for i=1,#团队副本数据 do
				if	团队副本数据[i].在团人员.状态=="" and 团队副本数据[i].申请人员.状态==""  then
				团队副本数据[i].在团人员.状态="在线"
				团队副本数据[i].申请人员.状态="在线"
				end
			end
		end
		if 玩家数据[数字id].战斗~=0 then
			if 玩家数据[数字id].队伍~=0 then
				队伍处理类:索取队伍信息(数字id,4004)
				if 玩家数据[数字id].队长 then
					发送数据(玩家数据[数字id].连接id,4006)
				end
			end
			玩家数据[数字id].道具:重置法宝回合(数字id)
			if 调试模式 then
				战斗准备类.战斗盒子[玩家数据[数字id].战斗]:强制结束战斗()
				if 玩家数据[数字id].队伍~=0 then
				 队伍处理类:退出队伍(数字id)
				end
			else
				战斗准备类.战斗盒子[玩家数据[数字id].战斗]:设置重连玩家(数字id)
			end
		else
			if 玩家数据[数字id].队伍~=0 then
				队伍处理类:退出队伍(数字id)
			end
		end
	else
		玩家数据[数字id]={连接id=id}
		玩家数据[数字id].角色=角色处理类:创建(id)
		玩家数据[数字id].道具=道具处理类:创建(数字id)
		玩家数据[数字id].召唤兽=召唤兽处理类:创建(数字id)
		玩家数据[数字id].道具:加载数据(账号,数字id)
		玩家数据[数字id].角色:加载数据(账号,数字id)
		玩家数据[数字id].召唤兽:加载数据(账号,数字id)
		玩家数据[数字id].神器=神器类:创建(数字id)
		玩家数据[数字id].神器:加载数据(账号,数字id)
		玩家数据[数字id].孩子=孩子处理类:创建(数字id)
		玩家数据[数字id].孩子:加载数据(账号,数字id)
		成就数据[数字id]={}
		成就数据[数字id]=成就处理类:创建(数字id)
		成就数据[数字id]:加载数据(账号,数字id)
		道具仓库数据[数字id]={}
		宝宝仓库数据[数字id]={}
		道具仓库数据[数字id].道具=道具仓库:创建(数字id)
		道具仓库数据[数字id].道具:加载数据(账号,数字id)
		宝宝仓库数据[数字id].召唤兽=召唤兽仓库:创建(数字id)
		宝宝仓库数据[数字id].召唤兽:加载数据(账号,数字id)
		玩家数据[数字id].账号=账号
		玩家数据[数字id].角色.账号=账号
		玩家数据[数字id].加锁=false
		玩家数据[数字id].物品锁时间={时间=os.time(),开关=false}
		玩家数据[数字id].结束等待=os.time()
		玩家数据[数字id].当前频道=os.time()
		玩家数据[数字id].世界频道=os.time()
		玩家数据[数字id].传闻频道=os.time()
		玩家数据[数字id].遇怪时间=os.time()+取随机数(10,20)
		玩家数据[数字id].队伍=0
		玩家数据[数字id].战斗=0
		玩家数据[数字id].观战=0
		玩家数据[数字id].角色.战斗开关 = false
		玩家数据[数字id].连接id=id
		玩家数据[数字id].商品列表=0
		if 玩家数据[数字id].角色.地图数据.编号 >=100000 and 玩家数据[数字id].角色.地图数据.编号 <=40000000 then
			for i=1,#房屋数据 do
				if 房屋数据[i].ID == 数字id then
					发送数据(id,119.2,房屋数据[i])
				end
			end
		end

		-- if 玩家数据[数字id].角色.地图数据.编号 >=100000 and 玩家数据[数字id].角色.地图数据.编号 <=40000000 then
	    --    玩家数据[数字id].角色.地图数据.编号=1001
	    --    玩家数据[数字id].角色.地图数据.x=237*20
	    --    玩家数据[数字id].角色.地图数据.y=114*20
	    -- end
		玩家数据[数字id].角色.连接id=id
		发送数据(id,5,玩家数据[数字id].角色:取总数据())
		玩家数据[数字id].道具:索要道具3(id,数字id)
		发送数据(id,16,玩家数据[数字id].召唤兽.数据)
		发送数据(id,25,宝宝队伍图)
		发送数据(id,997,{id=数字id,用户="正式用户",名称=玩家数据[数字id].角色.名称,账号=玩家数据[数字id].账号})
		发送数据(id,43,时辰信息.昼夜)
		发送数据(id,433,时辰信息.天气)
		发送数据(id,11,时辰信息.当前)
		发送数据(id,96.1,玩家数据[数字id].孩子.数据)
		商城处理类:取商品组数据(数字id, 18)
		地图处理类:加入玩家(数字id,玩家数据[数字id].角色.地图数据.编号,玩家数据[数字id].角色.地图数据.x,玩家数据[数字id].角色.地图数据.y)
		if 玩家数据[数字id].队伍~=0 then
			队伍处理类:退出队伍(数字id)
		end
		if 每日任务[数字id]==nil then
			 每日任务[数字id]={日常任务={},副本任务={},活跃度={当前=0,总活跃=0},签到数据={}}--{节日活动={},日常任务={},挑战竞技={},副本任务={},总活跃=0,领取活跃={}}
		end
		if 玩家数据[数字id].角色.变身数据 ~= nil and 玩家数据[数字id].角色:取任务(1) == 0 then
			玩家数据[数字id].角色:取消任务(玩家数据[数字id].角色:取任务(1))
			玩家数据[数字id].角色.变身数据 = nil
		end
		if 玩家数据[数字id]~=nil and 玩家数据[数字id].摊位数据~=nil then
			玩家数据[数字id].道具:收摊处理(玩家数据[数字id].连接id,数字id)
		end
		玩家数据[数字id].角色:更新任务()
		玩家数据[数字id].角色:刷新任务跟踪()
		玩家数据[数字id].角色:取快捷技能(数字id)
		玩家数据[数字id].角色.ip=ip
		if 玩家数据[数字id]~=nil and 玩家数据[数字id].角色 then
			self:检查装备(数字id,玩家数据[数字id].连接id)
			self:进入事件(数字id,玩家数据[数字id].连接id,false) -- 正常登录显示IP
			玩家数据[数字id].角色:刷新信息()
		end
		if 玩家数据[数字id].角色.BPMC ~= "无帮派" then
			local bh = 玩家数据[数字id].角色.BPBH
			if 取所有帮派[bh] and 取所有帮派[bh].已解散 then
				if 帮派数据[bh]==nil then -- 帮派已解散且数据已清理
					发送系统消息(数字id,"因为您所处的帮派解散，你现在已经是无帮派人士了！")
					-- 此时帮派数据已为nil，无需尝试删除称谓，直接重置玩家帮派信息
					玩家数据[数字id].角色.BG = 0
					玩家数据[数字id].角色.BPBH = 0
					玩家数据[数字id].角色.BPMC = "无帮派"
				elseif 帮派数据[bh].成员数据[数字id] then -- 帮派已解散但数据未完全清理，且玩家是成员
					-- 尝试删除称谓，但需要确保称谓存在，避免nil值错误
					if 帮派数据[bh].帮派名称 and 帮派数据[bh].成员数据[数字id].职务 then
						玩家数据[数字id].角色:删除称谓(帮派数据[bh].帮派名称..帮派数据[bh].成员数据[数字id].职务)
					end
					发送系统消息(数字id,"因为您所处的帮派解散，你现在已经是无帮派人士了！")
					玩家数据[数字id].角色.BG = 0
					玩家数据[数字id].角色.BPBH = 0
					玩家数据[数字id].角色.BPMC = "无帮派"
				else -- 帮派已解散但玩家不是成员
					玩家数据[数字id].角色.BG = 0
					玩家数据[数字id].角色.BPMC = "无帮派"
					玩家数据[数字id].角色.BPBH = 0
					发送系统消息(数字id,"#H/你被请离了帮派，你现在已经是无帮派人士了！")
				end
			elseif 帮派数据[bh] and 帮派数据[bh].成员数据[数字id] then -- 帮派未解散，玩家是成员
				帮派数据[bh].成员数据[数字id].在线 = true
				帮派数据[bh].成员数据[数字id].离线时间 = os.time()
				帮派数据[bh].成员数据[数字id].名称 = 玩家数据[数字id].角色.名称
				帮派数据[bh].成员数据[数字id].门派 = 玩家数据[数字id].角色.门派
				帮派数据[bh].成员数据[数字id].等级 = 玩家数据[数字id].角色.等级
			else
				if 帮派数据[bh] and 帮派数据[bh].成员数据[数字id]==nil then
					玩家数据[数字id].角色.BG = 0
					玩家数据[数字id].角色.BPMC = "无帮派"
					玩家数据[数字id].角色.BPBH = 0
					发送系统消息(数字id,"#H/你被请离了帮派，你现在已经是无帮派人士了！")
				end
			end
		end
		local 地图 = 玩家数据[数字id].角色.地图数据.编号
		if 地图==6001 or 地图==6002 or (地图>=7001 and 地图<=7005) or 地图==6003 or 地图==6004 or 地图==6005 or 地图==6006 or 地图==6007 or 地图==5002
			or 地图==6008 or 地图==6009 or 地图==6010 or 地图==6011 or 地图==7026 or 地图==7027 or 地图==7035 or 地图==7036 or 地图==2000 or 地图==7006 or 地图==7007 or 地图==7008 or 地图==7017 or 地图==7018 or 地图==7019 or 地图==7020 or 地图==6100 or 地图==7500 or 地图==7100 or 地图 == 8200 then
				地图处理类:跳转地图(数字id,1001,417,74)
		end
		local tx =""
		local ss=tostring(数字id)
		for i = 1, string.len(ss) do
			tx=tx.."#50"..string.sub(ss,i,i).."/"
		end
		if 玩家数据[数字id].角色.id特效 then
			广播消息({内容=format("玩家#Y/%s#W%s已经上线，欢迎来到#G梦幻西游",玩家数据[数字id].角色.名称,添加ID特效1(数字id)),频道="cw"})
		else
		 	广播消息({内容=format("玩家#Y/%s#W已经上线，欢迎来到#G梦幻西游",玩家数据[数字id].角色.名称,数字id),频道="cw"})
		end
	end
	self:加载进入信息(数字id,ip)
	self:清除进入数据(数字id)
	发送数据(玩家数据[数字id].连接id, 3705, 商城处理类.商品组[18])
	local fhz = 玩家数据[数字id].道具:临时背包索取()
	if fhz[2] then
		发送数据(玩家数据[数字id].连接id,303,{"底图框","临时背包闪烁",true})
	end
	if 玩家数据[数字id].角色.存银+玩家数据[数字id].角色.银子>=500000000 then
		银子jilu[数字id]={yz=玩家数据[数字id].角色.存银+玩家数据[数字id].角色.银子,zh=账号}
	end
end
function 系统处理类:是否有鲲鹏(id)
  for i,v in pairs(玩家数据[id].召唤兽.数据) do
    if v.模型 == "超级鲲鹏" or v.模型 == "进阶超级鲲鹏" then
      return true
    end
  end
  return false
end
function 系统处理类:助战进入游戏(id,账号,数字id,ip)
	数字id=数字id+0
	 LianjieID[id]=true
	if 玩家数据[数字id]~=nil then
		if 玩家数据[数字id].交易信息~=nil then
			玩家数据[数字id].道具:取消交易(数字id)
		end
		-- 设置助战重连状态标记
		玩家数据[数字id].重连状态 = true
		玩家数据[数字id].数据加载中 = true

		发送数据(玩家数据[数字id].连接id,998,"您的账号已经从另一台电脑上登录，如非本人操作，请立即修改游戏密码")
		玩家数据[数字id].连接id=id
		玩家数据[数字id].角色.连接id=id
		发送数据(id,5,玩家数据[数字id].角色:取总数据())
		玩家数据[数字id].道具:索要道具3(id,数字id)
		发送数据(id,16,玩家数据[数字id].召唤兽.数据)
		发送数据(id,25,宝宝队伍图)
		发送数据(id,997,{id=数字id,用户="正式用户",名称=玩家数据[数字id].角色.名称,账号=玩家数据[数字id].账号})
		发送数据(id,43,时辰信息.昼夜)
        发送数据(id,433,时辰信息.天气)
        发送数据(id,11,时辰信息.当前)
        发送数据(id,96.1,玩家数据[数字id].孩子.数据)
        -- 清理战斗自动栏数据
        发送数据(id,5505.5,{})
		玩家数据[数字id].角色:刷新任务跟踪()
		地图处理类:重连加入(数字id,玩家数据[数字id].角色.地图数据.编号,玩家数据[数字id].角色.地图数据.x,玩家数据[数字id].角色.地图数据.y)
		if 玩家数据[数字id].摊位数据~=nil then
			玩家数据[数字id].道具:收摊处理(玩家数据[数字id].连接id,数字id)
		end
		if 玩家数据[数字id].战斗~=0 then
			if 玩家数据[数字id].队伍~=0 then
				队伍处理类:索取队伍信息(数字id,4004)
				if 玩家数据[数字id].队长 then
					发送数据(玩家数据[数字id].连接id,4006)
				end
			end
			玩家数据[数字id].道具:重置法宝回合(数字id)
			if 调试模式 then
				 战斗准备类.战斗盒子[玩家数据[数字id].战斗]:强制结束战斗()
			else
				战斗准备类.战斗盒子[玩家数据[数字id].战斗]:设置重连玩家(数字id)
			end
		else
			 if 玩家数据[数字id].队伍~=0 then
				队伍处理类:退出队伍(数字id)
			end
		end
	else
		玩家数据[数字id]={连接id=id}
		玩家数据[数字id].角色=角色处理类:创建(id)
		玩家数据[数字id].道具=道具处理类:创建(数字id)
		玩家数据[数字id].召唤兽=召唤兽处理类:创建(数字id)
		玩家数据[数字id].道具:加载数据(账号,数字id)
		玩家数据[数字id].角色:加载数据(账号,数字id)
		玩家数据[数字id].召唤兽:加载数据(账号,数字id)
		玩家数据[数字id].神器=神器类:创建(数字id)
		玩家数据[数字id].神器:加载数据(账号,数字id)
		玩家数据[数字id].孩子=孩子处理类:创建(数字id)
		玩家数据[数字id].孩子:加载数据(账号,数字id)
		成就数据[数字id]={}
		成就数据[数字id]=成就处理类:创建(数字id)
		成就数据[数字id]:加载数据(账号,数字id)
		道具仓库数据[数字id]={}
		宝宝仓库数据[数字id]={}
		道具仓库数据[数字id].道具=道具仓库:创建(数字id)
		道具仓库数据[数字id].道具:加载数据(账号,数字id)
		宝宝仓库数据[数字id].召唤兽=召唤兽仓库:创建(数字id)
		宝宝仓库数据[数字id].召唤兽:加载数据(账号,数字id)
		玩家数据[数字id].账号=账号
		玩家数据[数字id].角色.账号=账号
        玩家数据[数字id].加锁=false
        玩家数据[数字id].物品锁时间={时间=os.time(),开关=false}
		玩家数据[数字id].结束等待=os.time()
		玩家数据[数字id].当前频道=os.time()
		玩家数据[数字id].世界频道=os.time()
		玩家数据[数字id].传闻频道=os.time()
		玩家数据[数字id].遇怪时间=os.time()+取随机数(10,20)
		玩家数据[数字id].队伍=0
		玩家数据[数字id].战斗=0
		玩家数据[数字id].观战=0
		玩家数据[数字id].角色.战斗开关 = false
		玩家数据[数字id].连接id=id
		玩家数据[数字id].商品列表=0
		玩家数据[数字id].角色.连接id=id
		发送数据(id,5,玩家数据[数字id].角色:取总数据())
		玩家数据[数字id].道具:索要道具3(id,数字id)
		发送数据(id,16,玩家数据[数字id].召唤兽.数据)
		发送数据(id,25,宝宝队伍图)
		发送数据(id,997,{id=数字id,用户="正式用户",名称=玩家数据[数字id].角色.名称,账号=玩家数据[数字id].账号})
		发送数据(id,43,时辰信息.昼夜)
        发送数据(id,433,时辰信息.天气)
        发送数据(id,11,时辰信息.当前)
        发送数据(id,96.1,玩家数据[数字id].孩子.数据)
        -- 清理战斗自动栏数据
        发送数据(id,5505.5,{})
		地图处理类:加入玩家(数字id,玩家数据[数字id].角色.地图数据.编号,玩家数据[数字id].角色.地图数据.x,玩家数据[数字id].角色.地图数据.y)
		if 玩家数据[数字id].队伍~=0 then
			 队伍处理类:退出队伍(数字id)
		end
		if 玩家数据[数字id].角色.变身数据 ~= nil and 玩家数据[数字id].角色:取任务(1) == 0 then
			玩家数据[数字id].角色:取消任务(玩家数据[数字id].角色:取任务(1))
			玩家数据[数字id].角色.变身数据 = nil
		end
		if 玩家数据[数字id]~=nil and 玩家数据[数字id].摊位数据~=nil then
			玩家数据[数字id].道具:收摊处理(玩家数据[数字id].连接id,数字id)
		end
		玩家数据[数字id].角色:更新任务()
		玩家数据[数字id].角色:刷新任务跟踪()
		玩家数据[数字id].角色:取快捷技能(数字id)
		玩家数据[数字id].角色.ip=ip
		if 玩家数据[数字id]~=nil and 玩家数据[数字id].角色 then
		   self:进入事件(数字id,玩家数据[数字id].连接id,true) -- 助战角色不显示IP
           self:检查装备(数字id,玩家数据[数字id].连接id)
		   玩家数据[数字id].角色:刷新信息()
		end
		if 玩家数据[数字id].角色.BPMC ~= "无帮派" then
			local bh = 玩家数据[数字id].角色.BPBH
			if 取所有帮派[bh] and 取所有帮派[bh].已解散 and 帮派数据[bh]==nil then
				 发送系统消息(数字id,"因为您所处的帮派解散，你现在已经是无帮派人士了！")
				 玩家数据[数字id].角色.BG = 0
				玩家数据[数字id].角色.BPBH = 0
				 玩家数据[数字id].角色.BPMC = "无帮派"
			elseif 帮派数据[bh] and 帮派数据[bh].成员数据[数字id] then
				帮派数据[bh].成员数据[数字id].在线 = true
				 帮派数据[bh].成员数据[数字id].离线时间 = os.time()
				 帮派数据[bh].成员数据[数字id].名称 = 玩家数据[数字id].角色.名称
				 帮派数据[bh].成员数据[数字id].门派 = 玩家数据[数字id].角色.门派
				 帮派数据[bh].成员数据[数字id].等级 = 玩家数据[数字id].角色.等级
			else
				if 帮派数据[bh] and 帮派数据[bh].成员数据[数字id]==nil then
					  玩家数据[数字id].角色.BG = 0
					 玩家数据[数字id].角色.BPMC = "无帮派"
					  玩家数据[数字id].角色.BPBH = 0
					  发送系统消息(数字id,"#H/你被请离了帮派，你现在已经是无帮派人士了！")
				end
			end
		end
		local tx =""
        local ss=tostring(数字id)
        for i = 1, string.len(ss) do
            tx=tx.."#50"..string.sub(ss,i,i).."/"
        end
        if 玩家数据[数字id].角色.id特效 then
			广播消息({内容=format("玩家#Y/%s#W%s已经上线，欢迎来到#G梦幻西游",玩家数据[数字id].角色.名称,添加ID特效1(数字id)),频道="cw"})
		else
		 	广播消息({内容=format("玩家#Y/%s#W已经上线，欢迎来到#G梦幻西游",玩家数据[数字id].角色.名称,数字id),频道="cw"})
		end
    end
	self:加载进入信息(数字id,ip)
	self:清除进入数据(数字id)
	local fhz = 玩家数据[数字id].道具:临时背包索取()
    if fhz[2] then
        发送数据(玩家数据[数字id].连接id,303,{"底图框","临时背包闪烁",true})
    end
    if 玩家数据[数字id].角色.存银+玩家数据[数字id].角色.银子>=500000000 then
        银子jilu[数字id]={yz=玩家数据[数字id].角色.存银+玩家数据[数字id].角色.银子,zh=账号}
    end
end
function 系统处理类:密码锁处理(id,nr)
	if 玩家数据[id].角色.密码锁==nil then
		玩家数据[id].角色.密码锁={是否加锁=false}
	end
	if  玩家数据[id].角色.密码锁.密码~=nil  then
		常规提示(id,"#Y/您已经加过锁了!!!!")
		return  0
	end
	if nr.ms1 == nr.ms2 then
		玩家数据[id].角色.密码锁.密码=nr.ms1
		玩家数据[id].角色.密码锁.是否加锁=true
		常规提示(id,"#Y/密码设定成功为"..nr.ms1)
	else
		常规提示(id,"#Y/请确认密码是否相同!!!!")
	end
end
function 系统处理类:解锁处理(id,nr)
	if 玩家数据[id].角色.密码锁==nil then
		玩家数据[id].角色.密码锁={是否加锁=false}
	end
	if  玩家数据[id].角色.密码锁.是否加锁==false  then
		常规提示(id,"#Y/您还未加锁,请先加锁")
		return  0
	end
	if nr.ms1==玩家数据[id].角色.密码锁.密码 then
		玩家数据[id].角色.密码锁.是否加锁=false
		常规提示(id,"#Y/恭喜您解锁成功")
	else
		常规提示(id,"#Y/密码错误")
	end
end
function 系统处理类:修改处理(id,nr)
	if 玩家数据[id].角色.密码锁==nil then
		玩家数据[id].角色.密码锁={是否加锁=false}
	end
	if  玩家数据[id].角色.密码锁.密码==nil  then
		常规提示(id,"#Y/您还未加锁,请先加锁")
		return  0
	end
	if nr.ms1 ~= 玩家数据[id].角色.密码锁.密码 then
		常规提示(id,"#Y/您输入的密码不正确")
		return 0
	end
	if nr.ms1 == nr.ms2 then
		常规提示(id,"#Y/不能输入相同的密码")
		return 0
	end
	玩家数据[id].角色.密码锁.密码=nr.ms2
	常规提示(id,"#Y/新密码设定成功为"..nr.ms2)
end
function 系统处理类:进入游戏检测(数字id)
	for i=1,6 do
		if 玩家数据[数字id].角色.装备[i] ~= nil then
			local 格子 = 玩家数据[数字id].角色.装备[i]
			if 玩家数据[数字id].道具.数据[格子].限时 ~= nil and 玩家数据[数字id].道具.数据[格子].限时 < os.time() then
				玩家数据[数字id].角色:卸下装备(玩家数据[数字id].道具.数据[格子],玩家数据[数字id].道具.数据[格子].分类,"0")
				常规提示(数字id,"#Y你的装备#R"..玩家数据[数字id].道具.数据[格子].名称.."#Y使用时间已到已被系统回收")
				玩家数据[数字id].角色.装备[i] = nil
				玩家数据[数字id].道具.数据[格子] = nil
			end
		end
	end
end
function 系统处理类:进入战斗检测(数字id)
	for i=1,6 do
		if 玩家数据[数字id].角色.装备[i] ~= nil then
			local 格子 = 玩家数据[数字id].角色.装备[i]
			if 玩家数据[数字id].道具.数据[格子].限时 ~= nil and 玩家数据[数字id].道具.数据[格子].限时 < os.time() then
				玩家数据[数字id].角色:卸下装备(玩家数据[数字id].道具.数据[格子],玩家数据[数字id].道具.数据[格子].分类,"0")
				发送数据(玩家数据[数字id].连接id,38,{内容="你的装备#R/"..玩家数据[数字id].道具.数据[格子].名称.."#W/使用时间已到已被系统回收",频道="xt"})
				玩家数据[数字id].角色.装备[i] = nil
				玩家数据[数字id].道具.数据[格子] = nil
			end
		end
	end
end
function 系统处理类:退出战斗检测(数字id)
	for i=1,6 do
		if 玩家数据[数字id].角色.装备[i] ~= nil then
			local 格子 = 玩家数据[数字id].角色.装备[i]
			if 玩家数据[数字id].道具.数据[格子].限时 ~= nil and 玩家数据[数字id].道具.数据[格子].限时 < os.time() then
				玩家数据[数字id].角色:卸下装备(玩家数据[数字id].道具.数据[格子],玩家数据[数字id].道具.数据[格子].分类,"0")
				发送数据(玩家数据[数字id].连接id,38,{内容="你的装备#R/"..玩家数据[数字id].道具.数据[格子].名称.."#W/使用时间已到已被系统回收",频道="xt"})
				玩家数据[数字id].角色.装备[i] = nil
				玩家数据[数字id].道具.数据[格子] = nil
			end
		end
	end
end
function 系统处理类:领取红包传音(id,内容)
	local 编号=内容.编号
	if 红包记录[编号]==nil then
		常规提示(id,"#Y/你来晚了,已经没有了#24")
		return
	end
	if 红包记录[编号].红包数量.领取>=红包记录[编号].红包数量.数量 then
		常规提示(id,"#Y/你来晚了,已经没有了#24")
		return
	end
	local 红包文件=获取新建数据(id,"红包记录")
	for k,v in pairs(红包记录[编号].领取记录) do
		if v.id==id then
			常规提示(id,"#Y/你已经领取过了,请给别人留点吧#24")
			return
		end
	end
	local 领取金额=0
	local 领取数量=红包记录[编号].红包数量.数量-红包记录[编号].红包数量.领取
	if 领取数量==1 and 红包记录[编号].金额>=1 then
		领取金额=红包记录[编号].金额
	else
		金额=红包记录[编号].金额/领取数量
		领取金额=取随机数(金额*0.5,金额*1.5)
		红包记录[编号].金额=红包记录[编号].金额-领取金额
		红包记录[编号].红包数量.领取=红包记录[编号].红包数量.领取+1
	end
	玩家数据[id].角色:添加银子(领取金额,"红包金额")
	广播消息({内容=format("#S/[大佬发红包]#R/%s#Y领取了由#G/%s#Y/发送的红包,金额高达#R/%s#37#37",玩家数据[id].角色.名称,红包记录[编号].发布者.名称,领取金额),频道="jj"})
	红包文件[#红包文件+1]={发布者=红包记录[编号].发布者.名称,红包金额=红包记录[编号].总金额,抢到红包=领取金额}
	红包记录[编号].领取记录[#红包记录[编号].领取记录+1]={id=id,名称=玩家数据[id].角色.名称,金额=领取金额}
	保存新建数据(id,"红包记录",红包文件)
	发送数据(玩家数据[id].连接id,131.5,{红包=红包记录[编号],金额=领取金额,内容=红包记录[编号].传音内容})
end
function 系统处理类:发布红包传音(id,内容)
	local 价格=40
	local 红包=0
	if 内容.红包金额~="" then
		红包=内容.红包金额+0
	end
	if 玩家数据[id].角色.银子<红包 then
		常规提示(id,"#Y/你没有足够的银子")
		return
	end
	local 传音内容=内容.传音内容
	if string.len(传音内容)>25 then
		常规提示(id,"#Y/你输入的字数过多,请重新输入(英文25个,中文12个)")
		return
	end
	if 红包传音冷却~=nil then
		if 红包传音冷却+30>os.time() then
			常规提示(id,"#Y/当前已经有玩家使用了红包功能,请稍等几秒")
			return
		end
	end
	if 内容.数量+0>20 then
		常规提示(id,"#Y/红包数量不能超过20个#56")
		return
	end
	local 红包编号=#红包记录+1
	红包记录[红包编号]={}
	红包记录[红包编号].发布者={名称=玩家数据[id].角色.名称,id=id,模型=玩家数据[id].角色.模型}
	红包记录[红包编号].金额=红包
	红包记录[红包编号].总金额=红包
	红包记录[红包编号].红包数量={领取=0,数量=内容.数量}
	红包记录[红包编号].领取记录={}
	红包记录[红包编号].传音内容=传音内容
	写出文件([[游戏数据/红包记录.txt]],table.tostring(红包记录))
	玩家数据[id].角色.银子=玩家数据[id].角色.银子-红包
	红包传音冷却=os.time()
	for n, v in pairs(玩家数据) do
		if  玩家数据[n]~= nil then
			发送数据(玩家数据[n].连接id,131.3,{编号=红包编号})
		end
	end
end
function 系统处理类:黑市拍卖(id)
	local 道具id=0
	local 格子id=0
	if 玩家数据[id].角色.等级<60 then
		发送数据(连接id,1501,{名称=玩家数据[id].角色.名称,模型=玩家数据[id].角色.模型,对话=format("只有等级达到60级的角色才可参加古玩拍卖")})
		return
	elseif 古玩数据[玩家数据[id].id]~=nil and 古玩数据[玩家数据[id].id]>=100 then
		发送数据(连接id,1501,{名称=玩家数据[id].角色.名称,模型=玩家数据[id].角色.模型,对话=format("你本日已经参加过100次拍卖了，请明日再来")})
		return
	end
	if 古玩数据[玩家数据[id].id]==nil then 古玩数据[玩家数据[id].id]=0 end
	古玩数据[玩家数据[id].id]=古玩数据[玩家数据[id].id]+1
	if 玩家数据[id].道具:消耗背包道具(玩家数据[id].连接id,id,"未鉴定的古董",1) then
		local 参数=math.random(1,100)
		local 计算流程={类型=0,动作={},银子=0}
		if 参数<=30 then
			计算流程.类型=1
		elseif 参数<=60 then
			计算流程.类型=2
		elseif 参数<=90 then
			计算流程.类型=3
			计算流程.银子=8888
			玩家数据[id].角色:添加银子(8888, "黑市拍卖",1)
		else
			local 银子=math.random(8888,88888)
			local 连击=math.random(1,11)
			local 初始银子=银子
			计算流程.类型=4
			计算流程.动作[1]=银子
			if math.random(100)<=5 then
				连击=11
			end
			for n=1,连击 do
				local 条件=math.random(100)
				银子=0
				if 条件<=15 then
					银子=银子+1000
				elseif 条件<=20  then
					银子=银子+math.floor(n*math.random(3000,8000))
				elseif 条件<=50  then
					银子=银子+math.floor(n*math.random(2000,20000))
				elseif 条件<=60  then
					银子=银子+math.floor(n*math.random(10000,20000))
				elseif 条件<=80  then
					银子=银子+math.floor(n*math.random(20000,30000))
				else
					银子=银子+math.floor(n*math.random(20000,50000))
				end
				if math.random(1,100)<=5 then
					银子=银子*2
				end
				初始银子=初始银子+银子
				计算流程.动作[#计算流程.动作+1]=初始银子
			end
			计算流程.银子=初始银子
			玩家数据[id].角色:添加银子(初始银子, "黑市拍卖",1)
			if 初始银子>=1000000 then
				广播消息(string.format("#xt/#S(黑市拍卖会)#R/%s#Y带着自己的宝贝在参加黑市拍卖会时，居然被拍出了#G/%s#Y两银子的天价",玩家数据[id].角色.名称,初始银子))
			end
		end
		发送数据(玩家数据[id].连接id,219.1,计算流程)
	else
		添加最后对话(id,"请先在古玩店老板处购买未鉴定的古董")
	end
end
function 系统处理类:自动抓鬼处理(内容)
	local id = 内容.数字id
	if 玩家数据[id].自动抓鬼~= nil then
		local 队伍id = 玩家数据[id].队伍
        local 平均等级 = 取队伍平均等级(队伍id, id)
        if  平均等级 < 40 then
            发送数据(玩家数据[id].连接id,220,{进程 = "关闭"})
            常规提示(id,"#Y/队伍平均等级不符合要求，需要大于40级")
            玩家数据[id].自动抓鬼 = nil
            return
        end
		if 玩家数据[id].队伍==0 or 玩家数据[id].队长==false  then
			发送数据(玩家数据[id].连接id,220,{进程 = "关闭"})
			常规提示(id,"#Y/该活动必须组队完成且由队长带领")
			常规提示(id,"#Y/"..玩家数据[id].自动抓鬼.."已关闭需要请重新开启")
			玩家数据[id].自动抓鬼=nil
			return
		end
		if 玩家数据[id].角色.银子<2000 then
			发送数据(玩家数据[id].连接id,220,{进程 = "关闭"})
			常规提示(id,"#Y/"..玩家数据[id].自动抓鬼.."已关闭需要请重新开启")
			玩家数据[id].自动抓鬼=nil
			return
		end
		if 内容.进程 == "第一进程" then
			if 玩家数据[id].自动抓鬼 == "自动抓鬼" then
				地图处理类:跳转地图(id,1122,52,62)
			else
				地图处理类:跳转地图(id,1125,30,24)
			end
			发送数据(玩家数据[id].连接id,220,{进程 = "第一进程"})
		elseif 内容.进程 == "第二进程" then
			if 玩家数据[id].自动抓鬼 == "自动抓鬼" then
				发送数据(玩家数据[id].连接id,1501,{名称="钟馗",模型="钟馗",对话="现在做鬼的也不安分，老是有出去闲逛的，你能帮我抓他们回来吗？",选项={"好的 我帮你","我来取消任务","不，我没有空"}})
			else
				发送数据(玩家数据[id].连接id,1501,{名称="黑无常",模型="黑无常",对话="地狱里头的那些鬼王已经无法镇压了，现在正在四处祸害人间。可惜仅凭我一己之力无法将他们全都收服。",选项={"我们来帮你","我怕鬼，再见"}})
			end
			发送数据(玩家数据[id].连接id,220,{进程 = "第二进程"})
		elseif 内容.进程 == "第三进程" then
			if 取队伍任务(玩家数据[id].队伍,8)  then
				local 队伍id=玩家数据[id].队伍
				for n=1,#队伍数据[队伍id].成员数据 do
					玩家数据[队伍数据[队伍id].成员数据[n]].角色:取消任务(玩家数据[队伍数据[队伍id].成员数据[n]].角色:取任务(8))
					玩家数据[队伍数据[队伍id].成员数据[n]].角色.捉鬼次数=1
					常规提示(队伍数据[队伍id].成员数据[n],"#Y/已经取消任务,同时当前捉鬼次数重置")
				end
			end
			if 取队伍任务(玩家数据[id].队伍,14)  then
				local 队伍id=玩家数据[id].队伍
				for n=1,#队伍数据[队伍id].成员数据 do
					玩家数据[队伍数据[队伍id].成员数据[n]].角色:取消任务(玩家数据[队伍数据[队伍id].成员数据[n]].角色:取任务(14))
					玩家数据[队伍数据[队伍id].成员数据[n]].角色.捉鬼次数=1
					常规提示(队伍数据[队伍id].成员数据[n],"#Y/已经取消任务,同时当前捉鬼次数重置")
				end
			end
			if 玩家数据[id].角色.银子>30000 then
				if 玩家数据[id].自动抓鬼 == "自动抓鬼" then
					设置任务8(id)
				else
					设置任务14(id)
				end
				发送数据(玩家数据[id].连接id,220,{进程 = "第三进程"})
			else
				发送数据(玩家数据[id].连接id,220,{进程 = "关闭"})
				玩家数据[id].自动抓鬼 = nil
				常规提示(id,"#Y/银两不足30000")
				return
			end
		elseif 内容.进程 == "第四进程" then
			local 任务id = 0
			if 玩家数据[id].自动抓鬼 == "自动抓鬼" then
				任务id =玩家数据[id].角色:取任务(8)
			else
				任务id =玩家数据[id].角色:取任务(14)
			end
			if 任务id~= 0 then
				地图处理类:跳转地图(id,任务数据[任务id].地图编号,任务数据[任务id].x+10,任务数据[任务id].y+10)
				发送数据(玩家数据[id].连接id,220,{进程 = "第四进程"})
			else
				发送数据(玩家数据[id].连接id,220,{进程 = "第六进程"})
			end
		elseif 内容.进程 == "第五进程" then
			local 任务id =0
			if 玩家数据[id].自动抓鬼 == "自动抓鬼" then
				任务id =玩家数据[id].角色:取任务(8)
			else
				任务id =玩家数据[id].角色:取任务(14)
			end
			if 任务id ~= 0 then
				发送数据(玩家数据[id].连接id,1501,{名称=任务数据[任务id].名称,模型=任务数据[任务id].模型,对话="悄悄地告诉你，其实我是从地狱里偷跑出来的#89",选项={"回你的地狱去","我只是路过"}})
				发送数据(玩家数据[id].连接id,220,{进程 = "第五进程"})
			else
				发送数据(玩家数据[id].连接id,220,{进程 = "第六进程"})
			end
		elseif 内容.进程 == "第六进程" then
			local 任务id =0
			if 玩家数据[id].自动抓鬼 == "自动抓鬼" then
				任务id =玩家数据[id].角色:取任务(8)
				if 任务id~=nil and  任务id~= 0  then
					战斗准备类:创建战斗(id+0,100008,任务id)
					任务数据[任务id].战斗=true
					玩家数据[id].地图单位=nil
				else
					发送数据(玩家数据[id].连接id,220,{进程 = "第六进程"})
				end
			else
				任务id =玩家数据[id].角色:取任务(14)
				if 任务id~=nil and 任务id~= 0 then
					战斗准备类:创建战斗(id+0,101000,任务id)
					任务数据[任务id].战斗=true
					玩家数据[id].地图单位=nil
				else
					发送数据(玩家数据[id].连接id,220,{进程 = "第六进程"})
				end
			end
		elseif 内容.进程 == "关闭" then
			常规提示(id,"#Y/"..玩家数据[id].自动抓鬼.."已关闭需要请重新开启")
			玩家数据[id].自动抓鬼=nil
		end
	end
end
function 系统处理类:小龟快跑结算()
	local 结算目标 = nil
	if 小龟快跑.第一名 == 1 then
		结算目标 = "小龟村一郎"
	elseif 小龟快跑.第一名 == 2 then
		结算目标 = "小龟蹦二郎"
	elseif 小龟快跑.第一名 == 3 then
		结算目标 = "小龟驰三郎"
	end
	for k,v in pairs(小龟快跑[结算目标]) do
		玩家数据[k].角色.小龟积分=玩家数据[k].角色.小龟积分+10
		玩家数据[k].角色:添加银子(4000*小龟快跑[结算目标][k].下注,"小龟快跑",1)
		发送系统消息(k,("恭喜你，因下注小鬼快跑活动，而赢得奖金#Y/"..4000*小龟快跑[结算目标][k].下注.."#W/两"))
		发送数据(玩家数据[k].连接id,38,{内容="你的点券分增加10点，现在是#R/"..玩家数据[k].角色.小龟积分.."点#W。",频道=""})
		广播消息({内容="经过激烈的角逐，在本次次赛中#Y/"..结算目标.."#W/获得第一名。共有#Y/"..小龟快跑[结算目标][k].下注.."#W/注幸运猜中，每注奖金#Y/4000#W/。",频道="xt"})
	end
	小龟快跑={开关=false,开始比赛=false,起始=os.time(),次数=0,次数2=0,次数3=0,小龟村一郎={},小龟蹦二郎={},小龟驰三郎={},第一名=0,随机事件={"休息","雷劈"},结束=20}
end


function 系统处理类:获取房子列表(id)
	local 房子列表={}
	for n=1,#房屋数据 do
	    房子列表[#房子列表+1]={id=房屋数据[n].ID,农业等级=房屋数据[n].农业等级.等级,房主=房屋数据[n].房主名称}
	end
	发送数据(玩家数据[id].连接id,154,房子列表)
end
return 系统处理类
