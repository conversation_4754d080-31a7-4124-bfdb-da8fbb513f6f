-- @Author: 小九呀丶 - QQ：5268416
-- @Date:   2025-06-05 17:59:45
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-06-11 19:41:54
-- @Author: 小九呀丶 - QQ：5268416
-- @Date:   2025-03-16 22:31:00
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-06-01 00:11:20
local 地图坐标类 = class()

function 地图坐标类:初始化(文件)
  --collectgarbage("collect")
  if 文件==5135 or 文件==5136 or 文件==5137 or 文件==5138 or 文件==5139  then
    文件=1197
  end
  self.dtwj = 文件+0
  self.mapzz = {}
  self.增加 = {x=0,y=0,z=0}
  self.db = {}
  -- __S服务:输出(文件)
  --self.map = require("Script/地图处理类/MAP")([[scene/]]..文件..".map")
  --地图数据存储[self.dtwj]={宽度=self.map.Width,高度=self.map.Height}
  --self.宽度,self.高度,self.行数,self.列数 = self.map.Width,self.map.Height,self.map.MapRowNum,self.map.MapColNum
  self.路径点={}
  self.map ={}
  self.寻路 ={}
  -- 添加已使用坐标点的记录
  self.已使用坐标 = {}
end

function 地图坐标类:取随机点()
  local 临时路径点 = {}
  local 返回点 = {}

  -- 尝试读取和解析坐标文件，添加错误检查
  local 文件内容 = 读入文件([[maplj\]]..self.dtwj..[[.txt]])
  if not 文件内容 or 文件内容 == "" then
    -- 文件读取失败，返回默认坐标
    return {x=50, y=50}
  end

  临时路径点 = table.loadstring(文件内容)
  if not 临时路径点 or #临时路径点 == 0 then
    -- 解析失败或数据为空，返回默认坐标
    return {x=50, y=50}
  end

  local 随机点=取随机数(1,#临时路径点)
  local 选中点 = 临时路径点[随机点]

  -- 检查选中的坐标点是否有效
  if not 选中点 or not 选中点.x or not 选中点.y then
    -- 坐标点无效，返回默认坐标
    return {x=50, y=50}
  end

  返回点 = {x=选中点.x, y=选中点.y}
  return 返回点
end

function 地图坐标类:取附近点(x,y)
  local 临时路径点 = {}
  local 返回点 = {}
  local 临时表 = {}
  临时路径点 = table.loadstring(读入文件([[maplj\]]..self.dtwj..[[.txt]]))

  -- 清理超过30秒的已使用坐标记录
  local 当前时间 = os.time()
  for 坐标, 时间 in pairs(self.已使用坐标) do
    if 当前时间 - 时间 > 30 then
      self.已使用坐标[坐标] = nil
    end
  end

  -- 收集符合条件的坐标点
  for k,v in pairs(临时路径点) do
    local 临时x,临时y = 临时路径点[k].x-x,临时路径点[k].y-y
    if 临时x > -10  and 临时x < 10 and 临时y > -10  and 临时y < 10 then
      local 坐标key = string.format("%d,%d", 临时路径点[k].x, 临时路径点[k].y)
      -- 检查该坐标是否已被使用
      if not self.已使用坐标[坐标key] then
        临时表[#临时表+1] = {x=临时路径点[k].x, y=临时路径点[k].y}
      end
    end
  end

  -- 如果没有可用坐标点，则重置已使用坐标并重新收集
  if #临时表 == 0 then
    self.已使用坐标 = {}
    for k,v in pairs(临时路径点) do
      local 临时x,临时y = 临时路径点[k].x-x,临时路径点[k].y-y
      if 临时x > -10  and 临时x < 10 and 临时y > -10  and 临时y < 10 then
        临时表[#临时表+1] = {x=临时路径点[k].x, y=临时路径点[k].y}
      end
    end
  end

  -- 检查临时表是否仍然为空
  if #临时表 == 0 then
    -- 如果仍然没有可用坐标，直接返回原始坐标
    返回点 = {x=x, y=y}
  else
    -- 从可用坐标中随机选择一个
    local 随机点 = 取随机数(1,#临时表)
    返回点 = {x=临时表[随机点].x, y=临时表[随机点].y}
  end

  -- 记录使用的坐标点
  local 坐标key = string.format("%d,%d", 返回点.x, 返回点.y)
  self.已使用坐标[坐标key] = 当前时间

  return 返回点
end

function 地图坐标类:更新(dt) end
function 地图坐标类:显示(x,y) end

-- 释放地图坐标类资源（只释放坐标点相关数据）
function 地图坐标类:释放()
  self.路径点 = {}
  self.已使用坐标 = {}
  self.mapzz = {}
  self.db = {}
  -- 不释放map对象和寻路对象，保持地图基础功能
  -- 注释掉垃圾回收，避免影响坐标数据的稳定性
  -- collectgarbage("collect")
end

return 地图坐标类
