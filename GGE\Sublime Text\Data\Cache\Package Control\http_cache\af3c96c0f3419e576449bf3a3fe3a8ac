{"$schema": "sublime://packagecontrol.io/schemas/channel", "libraries_cache": {"https://raw.githubusercontent.com/packagecontrol/channel/main/repository.json": [{"author": "adrian<PERSON>b", "description": "Reusable constraint types to use with typing.Annotated.", "issues": "https://github.com/annotated-types/annotated-types/issues", "name": "annotated-types", "releases": [{"date": "2024-05-20 21:33:24", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "1f02e8b43a8fbbc3f3e0d4f0f4bfc8131bcb4eebe8849b8e5c773f3a1c582a53", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl", "version": "0.7.0"}]}, {"author": "c0fec0de", "description": "Python Anytree module", "issues": "https://github.com/c0fec0de/anytree/issues", "name": "anytree", "releases": [{"date": "2025-04-08 21:06:29", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "4cbcf10df36b1f1cba131b7e487ff3edafc9d6e932a3c70071b5b768bab901ff", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/7b/98/f6aa7fe0783e42be3093d8ef1b0ecdc22c34c0d69640dfb37f56925cb141/anytree-2.13.0-py3-none-any.whl", "version": "2.13.0"}, {"date": "2020-01-15 01:22:23", "platforms": ["*"], "python_versions": ["3.3"], "sha256": "14c55ac77492b11532395049a03b773d14c7e30b22aa012e337b1e983de31521", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/a8/65/be23d8c3ecd68d40541d49812cd94ed0f3ee37eb88669ca15df0e43daed1/anytree-2.8.0-py2.py3-none-any.whl", "version": "2.8.0"}]}, {"author": "<PERSON>", "description": "Arrow is a Python library that offers a sensible and human-friendly approach to creating, manipulating, formatting and converting dates, times and timestamps.", "issues": "https://github.com/arrow-py/arrow/issues", "name": "arrow", "releases": [{"date": "2023-09-30 22:11:16", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "c728b120ebc00eb84e01882a6f5e7927a53960aa990ce7dd2b10f39005a67f80", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/f8/ed/e97229a566617f2ae958a6b13e7cc0f585470eac730a73e9e82c32a3cdd2/arrow-1.3.0-py3-none-any.whl", "version": "1.3.0"}, {"date": "2020-10-06 20:45:02", "platforms": ["*"], "python_versions": ["3.3"], "sha256": "e098abbd9af3665aea81bdd6c869e93af4feb078e98468dd351c383af187aac5", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/ca/bc/ebc1afb3c54377e128a01024c006f983d03ee124bc52392b78ba98c421b8/arrow-0.17.0-py2.py3-none-any.whl", "version": "0.17.0"}]}, {"author": "facelessuser", "description": "Backrefs regular expression wrapper.", "issues": "https://github.com/facelessuser/sublime-backrefs/issues", "name": "backrefs", "releases": [{"date": "2023-08-30 12:42:21", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/facelessuser/sublime-backrefs/zip/1.10.0", "version": "1.10.0"}]}, {"author": "<PERSON><PERSON>", "description": "Python parser for bash", "issues": "https://github.com/idank/bashlex", "name": "bashlex", "releases": [{"date": "2023-01-18 15:21:24", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "91d73a23a3e51711919c1c899083890cdecffc91d8c088942725ac13e9dcfffa", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/f4/be/6985abb1011fda8a523cfe21ed9629e397d6e06fb5bae99750402b25c95b/bashlex-0.18-py2.py3-none-any.whl", "version": "0.18"}]}, {"author": "<PERSON>", "description": "Beautiful Soup is a Python library for pulling data out of HTML and XML files - https://www.crummy.com/software/BeautifulSoup/", "issues": "https://bugs.launchpad.net/beautifulsoup/", "name": "beautifulsoup4", "releases": [{"date": "2025-04-15 17:05:12", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "9bbbb14bfde9d79f38b8cd5f8c7c85f4b8f2523190ebed90e950a8dea4cb1c4b", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/50/cd/30110dc0ffcf3b131156077b90e9f60ed75711223f306da4db08eff8403b/beautifulsoup4-4.13.4-py3-none-any.whl", "version": "4.13.4"}, {"date": "2021-09-08 00:16:06", "platforms": ["*"], "python_versions": ["3.3"], "sha256": "9a315ce70049920ea4572a4055bc4bd700c940521d36fc858205ad4fcde149bf", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/69/bf/f0f194d3379d3f3347478bd267f754fc68c11cbf2fe302a6ab69447b1417/beautifulsoup4-4.10.0-py3-none-any.whl", "version": "4.10.0"}]}, {"author": "KuttKatrea", "description": "Easily implement OS, Host and OS/Host-level settings aside the normal user-level settings for any plugin.", "issues": "https://github.com/KuttKatrea/sublime-better-settings/issues", "name": "better_settings", "releases": [{"date": "2020-03-01 00:28:39", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/KuttKatrea/sublime-better-settings/zip/1.0.0", "version": "1.0.0"}]}, {"author": "revmis<PERSON>", "description": "Python boto3 module for Amazon Web Services", "issues": "https://github.com/revmischa/sublime-boto3/issues", "name": "boto3", "releases": [{"date": "2016-10-02 20:26:32", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/revmischa/sublime-boto3/zip/v2.0.0", "version": "2.0.0"}]}, {"author": "facelessuser", "description": "Bracex creates arbitrary strings via brace expansion much like <PERSON><PERSON>'s.", "issues": "https://github.com/facelessuser/sublime-bracex/issues", "name": "bracex", "releases": [{"date": "2024-08-03 03:59:49", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "d2fcf4b606a82ac325471affe1706dd9bbaa3536c91ef86a31f6b766f3dad1d0", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/05/4f/54d324c35221c027ca77e9aae418f525003bd0cc2613eea162a1246b5a92/bracex-2.5-py3-none-any.whl", "version": "2.5"}, {"date": "2021-02-10 22:03:19", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/facelessuser/sublime-bracex/zip/2.1.1", "version": "2.1.1"}]}, {"author": "<PERSON>berg", "description": "Independent BSON codec for Python that doesn’t depend on MongoDB - https://github.com/py-bson/bson", "issues": "https://github.com/idleberg/sublime-bson/issues", "name": "bson", "releases": [{"date": "2017-09-08 21:32:23", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/idleberg/sublime-bson/zip/0.4.8", "version": "0.4.8"}]}, {"author": "wbond", "description": "Python bz2 module", "issues": "https://github.com/codexns/sublime-bz2/issues", "name": "bz2", "releases": [{"date": "2014-12-04 17:19:43", "platforms": ["osx-x64", "linux-x32", "linux-x64", "windows-x32", "windows-x64"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/codexns/sublime-bz2/zip/1.0.0", "version": "1.0.0"}]}, {"author": "evandrocoan", "description": "Manages packages installed as git submodules", "issues": "https://github.com/evandrocoan/channelmanager/issues", "name": "channelmanager", "releases": [{"date": "2021-10-13 05:46:35", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/evandrocoan/channelmanager/zip/3999", "version": "3999"}]}, {"author": "python-cffi", "description": "Foreign Function Interface for Python calling C code.", "issues": "https://github.com/python-cffi/cffi/issues", "name": "cffi", "releases": [{"date": "2024-09-04 20:44:59", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "78122be759c3f8a014ce010908ae03364d00a1f81ab5c7f4a7a5120607ea56e1", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/2f/70/80c33b044ebc79527447fd4fbc5455d514c3bb840dede4455de97da39b4d/cffi-1.17.1-cp38-cp38-win_amd64.whl", "version": "1.17.1"}, {"date": "2024-09-04 20:44:47", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "636062ea65bd0195bc012fea9321aca499c0504409f413dc88af450b57ffd03b", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/48/08/15bf6b43ae9bd06f6b00ad8a91f5a8fe1069d4c9fab550a866755402724e/cffi-1.17.1-cp38-cp38-macosx_10_9_x86_64.whl", "version": "1.17.1"}, {"date": "2024-09-04 20:44:57", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "0e2b1fac190ae3ebfe37b979cc1ce69c81f4e4fe5746bb401dca63a9062cdaf1", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/bb/19/b51af9f4a4faa4a8ac5a0e5d5c2522dcd9703d07fac69da34a36c4d960d3/cffi-1.17.1-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "version": "1.17.1"}, {"date": "2024-09-04 20:44:51", "platforms": ["linux-arm64"], "python_versions": ["3.8"], "sha256": "e221cf152cff04059d011ee126477f0d9588303eb57e88923578ace7baad17f9", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/53/93/7e547ab4105969cc8c93b38a667b82a835dd2cc78f3a7dad6130cfd41e1d/cffi-1.17.1-cp38-cp38-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "version": "1.17.1"}]}, {"author": "chardet", "description": "The Universal Character Encoding Detector", "issues": "https://github.com/chardet/chardet/issues", "name": "chardet", "releases": [{"date": "2023-08-01 19:23:00", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "e1cf59446890a00105fe7b7912492ea04b6e6f06d4b742b2c788469e34c82970", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/38/6f/f5fbc992a329ee4e0f288c1fe0e2ad9485ed064cac731ed2fe47dcc38cbf/chardet-5.2.0-py3-none-any.whl", "version": "5.2.0"}, {"date": "2020-12-10 19:35:32", "platforms": ["*"], "python_versions": ["3.3"], "sha256": "f864054d66fd9118f2e67044ac8981a54775ec5b67aed0441892edb553d21da5", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/19/c7/fa589626997dd07bd87d9269342ccb74b1720384a4d739a1872bd84fbe68/chardet-4.0.0-py2.py3-none-any.whl", "version": "4.0.0"}]}, {"author": "Ahmed TAHRI", "description": "The Real First Universal Charset Detector. Open, modern and actively maintained alternative to Chardet.", "issues": "https://github.com/jawah/charset_normalizer/issues", "name": "charset-normalizer", "releases": [{"date": "2025-05-02 08:34:10", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "70f7172939fdf8790425ba31915bfbe8335030f05b9913d7ae00a87d4395620a", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/7c/02/1c82646582ccf2c757fa6af69b1a3ea88744b8d2b4ab93b7686b2533e023/charset_normalizer-3.4.2-cp38-cp38-win_amd64.whl", "version": "3.4.2"}, {"date": "2025-05-02 08:34:08", "platforms": ["windows-x32"], "python_versions": ["3.8"], "sha256": "8272b73e1c5603666618805fe821edba66892e2870058c94c53147602eab29c7", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/41/eb/c7367ac326a2628e4f05b5c737c86fe4a8eb3ecc597a4243fc65720b3eeb/charset_normalizer-3.4.2-cp38-cp38-win32.whl", "version": "3.4.2"}, {"date": "2025-05-02 08:33:45", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "76af085e67e56c8816c3ccf256ebd136def2ed9654525348cfa744b6802b69eb", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/4c/fd/f700cfd4ad876def96d2c769d8a32d808b12d1010b6003dc6639157f99ee/charset_normalizer-3.4.2-cp38-cp38-macosx_10_9_universal2.whl", "version": "3.4.2"}, {"date": "2025-05-02 08:33:54", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "6333b3aa5a12c26b2a4d4e7335a28f1475e0e5e17d69d55141ee3cab736f66d1", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/2e/bb/d76d3d6e340fb0967c43c564101e28a78c9a363ea62f736a68af59ee3683/charset_normalizer-3.4.2-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "version": "3.4.2"}, {"date": "2025-05-02 08:33:47", "platforms": ["linux-arm64"], "python_versions": ["3.8"], "sha256": "e45ba65510e2647721e35323d6ef54c7974959f6081b58d4ef5d87c60c84919a", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/3a/95/6eec4cbbbd119e6a402e3bfd16246785cc52ce64cf21af2ecdf7b3a08e91/charset_normalizer-3.4.2-cp38-cp38-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "version": "3.4.2"}, {"date": "2024-10-09 07:39:41", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "cab5d0b79d987c67f3b9e9c53f54a61360422a5a0bc075f43cab5621d530c3b6", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/94/d4/2b21cb277bac9605026d2d91a4a8872bc82199ed11072d035dc674c27223/charset_normalizer-3.4.0-cp38-cp38-macosx_10_9_x86_64.whl", "version": "3.4.0"}]}, {"author": "<PERSON>", "description": "KDL reading and writing using a C backend", "issues": "https://github.com/tjol/ckdl/issues", "name": "ckdl", "releases": [{"date": "2024-12-21 19:43:05", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "4b8f2e32637ca56def24c32849eec0c6e14b81dc1baad678c183bb034c92c5d7", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/7c/37/080936e5a48dd51ba1c6a5f5f8c2f9ff09005d4e90b24a6f094e7c6e221e/ckdl-1.0-cp38-cp38-win_amd64.whl", "version": "1.0"}, {"date": "2024-12-21 19:43:03", "platforms": ["windows-x32"], "python_versions": ["3.8"], "sha256": "59a117cf2c88f79a5dea314cc07e61d6a415bf2215c16a845930d0331e841d87", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/a2/3a/a198267d6c02ec33b40b655aa1b2b866112f27c8d23242617deb4c48fcc1/ckdl-1.0-cp38-cp38-win32.whl", "version": "1.0"}, {"date": "2024-12-21 19:42:52", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "0162325c9a931732f066b13f220b8c504dbc368e587c267fd524e9d814a8fd5b", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/f1/07/8738476598f5ea9b6980c8d5d51575cc5ffee785e933221f424efcf2ea3b/ckdl-1.0-cp38-cp38-macosx_10_9_x86_64.whl", "version": "1.0"}, {"date": "2024-12-21 19:42:54", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "20b925eef2775029c7e924294f78cd9460b392dc442d7495fc64c06db16ef200", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/2f/10/fd30a7fde6487c6b5c59fb68bade3e1a278b340fadfbbb1a8292861fd446/ckdl-1.0-cp38-cp38-macosx_11_0_arm64.whl", "version": "1.0"}, {"date": "2024-12-21 19:42:55", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "cdd7e807604061d6093fbecf15c42d38fca79b97201648b352e8e49bccbed77e", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/8f/92/846549d0b9eee8c8effcc043eab1e7fb31fb9aa869ad1b768bcc8cc892f2/ckdl-1.0-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "version": "1.0"}, {"date": "2024-12-21 19:42:57", "platforms": ["linux-x32"], "python_versions": ["3.8"], "sha256": "8a2a4af440228426808b56e54a0c1abd1a8fb76a3de97d158801caf57817c9e3", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/1b/bc/c58760eb00cc6c79a2f3f38dad755b9b74df56b87ff34ad661ab4303c9d1/ckdl-1.0-cp38-cp38-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", "version": "1.0"}]}, {"author": ["alemi", "cschen", "fraaz", "frel<PERSON>v"], "description": "codemp - collaborative editing library", "issues": "https://github.com/hexedtech/codemp/issues", "name": "codemp", "releases": [{"date": "2025-02-15 15:23:49", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "5692497329a9e253cf0ba13187550bf4c1b22c66f7fe5a22dc5b4141ee37cced", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/8e/18/cbea9cb4c35f61bc257583fa8e2874920bd84bd3591ab3e90fa8da2c8e0c/codemp-0.8.5-cp38-abi3-macosx_11_0_arm64.whl", "version": "0.8.5"}, {"date": "2025-02-15 15:23:47", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "ae7337eb2461ee7994ab2e1240041958f661a295523f119996bbcc7f6ec3ca26", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/cb/02/862725a4fffc556e10360d4ec35353ee1f2af5576eb03091cf5717738663/codemp-0.8.5-cp38-abi3-manylinux_2_34_x86_64.whl", "version": "0.8.5"}, {"date": "2024-10-26 18:18:19", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "0ff1c3d978c1e0f858a14a5fbac786fce697da5943862b4f34541f0e3f882f56", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/63/eb/1b7a9bfb6c2d1c328bf74ffec56e96c11432354a901a8be855bcc1820ef3/codemp-0.8.2-cp38-none-win_amd64.whl", "version": "0.8.2"}]}, {"author": "facelessuser", "description": "A color library.", "issues": "https://github.com/facelessuser/sublime-coloraide/issues", "name": "coloraide", "releases": [{"date": "2022-08-15 13:45:37", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/facelessuser/sublime-coloraide/zip/1.2.0", "version": "1.2.0"}]}, {"author": "evandrocoan", "description": "An additional concurrent file log handler for Python's standard logging package", "issues": "https://github.com/evandroforks/concurrentloghandler/issues", "name": "concurrentloghandler", "releases": [{"date": "2018-12-16 00:49:13", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/evandroforks/concurrentloghandler/zip/3999", "version": "3999"}]}, {"author": "<PERSON> and 96 others", "description": "Code coverage testing for Python", "issues": "https://github.com/codexns/sublime-coverage/issues", "name": "coverage", "releases": [{"date": "2024-08-04 19:45:06", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "3f1156e3e8f2872197af3840d8ad307a9dd18e615dc64d9ee41696f287c57ad8", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/5c/1c/96cf86b70b69ea2b12924cdf7cabb8ad10e6130eab8d767a1099fbd2a44f/coverage-7.6.1-cp38-cp38-win_amd64.whl", "version": "7.6.1"}, {"date": "2024-08-04 19:45:03", "platforms": ["windows-x32"], "python_versions": ["3.8"], "sha256": "da511e6ad4f7323ee5702e6633085fb76c2f893aaf8ce4c51a0ba4fc07580ea7", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/25/ee/b4c246048b8485f85a2426ef4abab88e48c6e80c74e964bea5cd4cd4b115/coverage-7.6.1-cp38-cp38-win32.whl", "version": "7.6.1"}, {"date": "2024-08-04 19:44:47", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "6db04803b6c7291985a761004e9060b2bca08da6d04f26a7f2294b8623a0c1a0", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/81/d0/d9e3d554e38beea5a2e22178ddb16587dbcbe9a1ef3211f55733924bf7fa/coverage-7.6.1-cp38-cp38-macosx_10_9_x86_64.whl", "version": "7.6.1"}, {"date": "2024-08-04 19:44:49", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "f1adfc8ac319e1a348af294106bc6a8458a0f1633cc62a1446aebc30c5fa186a", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/38/ea/cab2dc248d9f45b2b7f9f1f596a4d75a435cb364437c61b51d2eb33ceb0e/coverage-7.6.1-cp38-cp38-macosx_11_0_arm64.whl", "version": "7.6.1"}, {"date": "2024-08-04 19:44:55", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "8929543a7192c13d177b770008bc4e8119f2e1f881d563fc6b6305d2d0ebe9de", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/e4/6e/885bcd787d9dd674de4a7d8ec83faf729534c63d05d51d45d4fa168f7102/coverage-7.6.1-cp38-cp38-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "version": "7.6.1"}, {"date": "2024-08-04 19:44:53", "platforms": ["linux-x32"], "python_versions": ["3.8"], "sha256": "b43c03669dc4618ec25270b06ecd3ee4fa94c7f9b3c14bae6571ca00ef98b0d3", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/a6/94/d3055aa33d4e7e733d8fa309d9adf147b4b06a82c1346366fc15a2b1d5fa/coverage-7.6.1-cp38-cp38-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", "version": "7.6.1"}, {"date": "2024-08-04 19:44:51", "platforms": ["linux-arm64"], "python_versions": ["3.8"], "sha256": "a95324a9de9650a729239daea117df21f4b9868ce32e63f8b650ebe6cef5595b", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/ca/6f/f82f9a500c7c5722368978a5390c418d2a4d083ef955309a8748ecaa8920/coverage-7.6.1-cp38-cp38-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "version": "7.6.1"}, {"date": "2019-07-29 15:29:28", "platforms": ["osx-x64"], "python_versions": ["3.3"], "sha256": "6b62544bb68106e3f00b21c8930e83e584fdca005d4fffd29bb39fb3ffa03cb5", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/3b/2f/c641609b79e292a4a29375c4af0cf8156c36a0613000513b05eb1a838a59/coverage-4.5.4-cp33-cp33m-macosx_10_10_x86_64.whl", "version": "4.5.4"}, {"date": "2018-02-10 20:39:36", "platforms": ["linux-x64"], "python_versions": ["3.3"], "sha256": "5a13ea7911ff5e1796b6d5e4fbbf6952381a611209b736d48e675c2756f3f74e", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/ce/e5/7d0c5440de5ba0075b304478d6309b16cb4681063050b192d8d9398aa7f0/coverage-4.5.1-cp33-cp33m-manylinux1_x86_64.whl", "version": "4.5.1"}, {"date": "2018-02-10 20:39:35", "platforms": ["linux-x32"], "python_versions": ["3.3"], "sha256": "701cd6093d63e6b8ad7009d8a92425428bc4d6e7ab8d75efbb665c806c1d79ba", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/fa/fb/f058805db61a6ac8cce5121c5a8a1841239af1b69fac59cbd8d7bfbf8015/coverage-4.5.1-cp33-cp33m-manylinux1_i686.whl", "version": "4.5.1"}, {"date": "2016-07-26 21:09:17", "platforms": ["windows-x64"], "python_versions": ["3.3"], "sha256": "bd4eba631f07cae8cdb9c55c144f165649e6701b962f9d604b4e00cf8802406c", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/b1/55/02815cb8abb091033abb979ebde5122bb33b85c5987dede9ccd019033d19/coverage-4.2-cp33-cp33m-win_amd64.whl", "version": "4.2"}, {"date": "2016-07-26 21:09:13", "platforms": ["windows-x32"], "python_versions": ["3.3"], "sha256": "38e87c46d364b8b3ac4d161586707345b7bc7b16855be1751345fc91be702ff7", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/a0/34/1185348cc5c541bbdf107438f0f0ea9df5d9a4233a974e9228b6ee815489/coverage-4.2-cp33-cp33m-win32.whl", "version": "4.2"}]}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Python crypt module", "issues": "https://github.com/adnanyaqoobvirk/sublime-crypt/issues", "name": "crypt", "releases": [{"date": "2016-03-28 08:01:48", "platforms": ["linux-x32", "linux-x64"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/adnanyaqoobvirk/sublime-crypt/zip/v0.1.0", "version": "0.1.0"}]}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "description": "PyCryptodome is a self-contained Python package of low-level cryptographic primitives.", "issues": "https://github.com/Legrandin/pycryptodome/issues", "name": "pycryptodome", "releases": [{"date": "2025-05-17 17:21:10", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "c75b52aacc6c0c260f204cbdd834f76edc9fb0d8e0da9fbf8352ef58202564e2", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/54/2f/e97a1b8294db0daaa87012c24a7bb714147c7ade7656973fd6c736b484ff/pycryptodome-3.23.0-cp37-abi3-win_amd64.whl", "version": "3.23.0"}, {"date": "2025-05-17 17:20:50", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "cfb5cd445280c5b0a4e6187a7ce8de5a07b5f3f897f235caa11f1f435f182843", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/6e/4e/a066527e079fc5002390c8acdd3aca431e6ea0a50ffd7201551175b47323/pycryptodome-3.23.0-cp37-abi3-macosx_10_9_x86_64.whl", "version": "3.23.0"}, {"date": "2025-05-17 17:20:47", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "187058ab80b3281b1de11c2e6842a357a1f71b42cb1e15bce373f3d238135c27", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/db/6c/a1f71542c969912bb0e106f64f60a56cc1f0fabecf9396f45accbe63fa68/pycryptodome-3.23.0-cp37-abi3-macosx_10_9_universal2.whl", "version": "3.23.0"}, {"date": "2025-05-17 17:20:55", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "c8987bd3307a39bc03df5c8e0e3d8be0c4c3518b7f044b0f4c15d1aa78f52575", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/5f/e9/a09476d436d0ff1402ac3867d933c61805ec2326c6ea557aeeac3825604e/pycryptodome-3.23.0-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "version": "3.23.0"}, {"date": "2025-05-17 17:20:52", "platforms": ["linux-arm64"], "python_versions": ["3.8"], "sha256": "67bd81fcbe34f43ad9422ee8fd4843c8e7198dd88dd3d40e6de42ee65fbe1490", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/50/52/adaf4c8c100a8c49d2bd058e5b551f73dfd8cb89eb4911e25a0c469b6b4e/pycryptodome-3.23.0-cp37-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "version": "3.23.0"}]}, {"author": "<PERSON>berg", "description": "A Coffescript Object Notation (CSON) parser for Python - https://github.com/avakar/pycson", "issues": "https://github.com/idleberg/sublime-cson/issues", "name": "cson", "releases": [{"date": "2021-04-13 21:29:29", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/idleberg/sublime-cson/zip/0.8.0", "version": "0.8.0"}]}, {"author": "<PERSON>", "description": "The dateutil module provides powerful extensions to the datetime module available in the Python standard library", "issues": "https://github.com/dateutil/dateutil/issues", "name": "dateutil", "releases": [{"date": "2024-03-01 03:52:51", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sha256": "cbf2f1da5e6083ac2fbfd4da39a25f34312230110440f424a14c7558bb85d82e", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/13/7f/98d6f9ca8b731506c85785bbb8806c01f5966a4df6d68c0d1cf3b16967e1/python_dateutil-2.9.0-py2.py3-none-any.whl", "version": "2.9.0"}]}, {"author": "evandrocoan", "description": "Alternate simplified logging support and general utilities functions", "issues": "https://github.com/evandrocoan/debugtools/issues", "name": "debugtools", "releases": [{"date": "2021-06-07 23:41:04", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/evandrocoan/debugtools/zip/3999", "version": "3999"}]}, {"author": "<PERSON>berg", "description": "Converts a Python dictionary or other native data type into a valid XML string - https://github.com/idleberg/sublime-dicttoxml", "issues": "https://github.com/idleberg/sublime-dicttoxml/issues", "name": "dicttoxml", "releases": [{"date": "2016-09-06 10:29:53", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/idleberg/sublime-dicttoxml/zip/1.7.4", "version": "1.7.4"}]}, {"author": "evandrocoan", "description": "Autored by <PERSON> and others, <PERSON><PERSON> Patch is a high-performance library in multiple languages that manipulates plain text", "issues": "https://github.com/evandroforks/diffmatchpatch/issues", "name": "diffmatchpatch", "releases": [{"date": "2020-10-13 23:26:44", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/evandroforks/diffmatchpatch/zip/3999", "version": "3999"}]}, {"author": "<PERSON>berg", "description": "A library for using Unicode emoji annotations - https://github.com/kcsaff/emojitations", "issues": "https://github.com/idleberg/sublime-emojitations/issues", "name": "emojitations", "releases": [{"date": "2016-10-23 12:02:10", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/idleberg/sublime-emojitations/zip/0.1.0", "version": "0.1.0"}]}, {"author": "FichteFoll", "description": "Python enum module", "issues": "https://github.com/packagecontrol/enum/issues", "name": "enum", "releases": [{"date": "2018-01-25 02:22:58", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/enum/zip/v1.1.6", "version": "1.1.6"}]}, {"author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Like `typing._eval_type`, but lets older Python versions use newer typing features.", "issues": "https://github.com/alexmojaki/eval_type_backport/issues", "name": "eval-type-backport", "releases": [{"date": "2024-12-21 20:09:44", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "cb6ad7c393517f476f96d456d0412ea80f0a8cf96f6892834cd9340149111b0a", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/ce/31/55cd413eaccd39125368be33c46de24a1f639f2e12349b0361b4678f3915/eval_type_backport-0.2.2-py3-none-any.whl", "version": "0.2.2"}]}, {"author": "randy3k", "description": "GateOne terminal", "issues": "https://github.com/packagecontrol/gateone-terminal/issues", "name": "gateone-terminal", "releases": [{"date": "2018-06-18 05:08:33", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/gateone-terminal/zip/v1.1.0", "version": "1.1.0"}]}, {"author": "facelessuser", "description": "Growl Notification Transport Protocol library.", "issues": "https://github.com/facelessuser/sublime-gntp/issues", "name": "gntp", "releases": [{"date": "2016-12-03 16:28:28", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/facelessuser/sublime-gntp/zip/1.0.3", "version": "1.0.3"}]}, {"author": "Go Authors", "description": "A library for Go environment configuration", "issues": "https://github.com/golang/sublime-config/issues", "name": "golangconfig", "releases": [{"date": "2015-11-02 21:01:59", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/golang/sublime-config/zip/v0.9.0", "version": "0.9.0"}]}, {"author": "<PERSON>", "description": "Internationalized Domain Names in Applications (IDNA)", "issues": "https://github.com/kjd/idna/issues", "name": "idna", "releases": [{"date": "2024-09-15 18:07:37", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "946d195a0d259cbba61165e88e65941f16e9b36ea6ddb97f00452bae8b1287d3", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl", "version": "3.10"}]}, {"author": "<PERSON>", "description": "Jedi is a static analysis tool for Python that is typically used in IDEs/editors plugins", "issues": "https://github.com/david<PERSON>er/jedi/issues", "name": "jedi", "releases": [{"date": "2024-11-11 01:41:40", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "a8ef22bde8490f57fe5c7681a3c83cb58874daf72b4784de3cce5b6ef6edb5b9", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/c0/5a/9cac0c82afec3d09ccd97c8b6502d48f165f9124db81b4bcb90b4af974ee/jedi-0.19.2-py2.py3-none-any.whl", "version": "0.19.2"}]}, {"author": "jf<PERSON><PERSON>", "description": "A Chinese text segementation lib.", "issues": "https://github.com/packagecontrol/jieba/issues", "name": "ji<PERSON>a", "releases": [{"date": "2020-09-26 11:55:34", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/jieba/zip/1.0.0", "version": "1.0.0"}]}, {"author": "FichteFoll", "description": "Python Jinja2 module", "issues": "https://github.com/packagecontrol/jinja2/issues", "name": "Jinja2", "releases": [{"date": "2024-01-10 23:12:19", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "7d6d50dd97d52cbc355597bd845fabfbac3f551e1f99619e39a35ce8c370b5fa", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/30/6d/6de6be2d02603ab56e72997708809e8a5b0fbfee080735109b40a3564843/Jinja2-3.1.3-py3-none-any.whl", "version": "3.1.3"}, {"date": "2019-07-11 15:26:20", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/jinja2/zip/v2.10.1", "version": "2.10.1"}]}, {"author": "james<PERSON>", "description": "JSON Matching Expressions", "issues": "https://github.com/jmespath/jmespath.py/issues", "name": "jmespath", "releases": [{"date": "2022-06-17 18:00:10", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "02e2e4cc71b5bcab88332eebf907519190dd9e6e82107fa7f83b1003a6252980", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/31/b4/b9b800c45527aadd64d5b442f9b932b00648617eb5d63d2c7a6587b7cafc/jmespath-1.0.1-py3-none-any.whl", "version": "1.0.1"}]}, {"author": "kylebebak", "description": "An(other) implementation of JSON Schema for Python", "issues": "https://github.com/kylebebak/sublime-jsonschema/issues", "name": "jsonschema", "releases": [{"date": "2017-06-02 23:13:03", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/kylebebak/sublime-jsonschema/zip/v2.6.0", "version": "2.6.0"}]}, {"author": "<PERSON><PERSON><PERSON>", "description": "Sublime LLM Communication Core", "issues": "https://github.com/yaroslavyaroslav/llm_runner/issues", "name": "llm_runner", "releases": [{"date": "2025-05-09 09:47:45", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "a10245bd4624f99401ac8b75fe73bfc635792de7d692f228ca3e3b58e1a49f86", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/c1/23/92b676f1b23a23661ccc31915255c6075b482c092c3e86d53c03edbc38cb/llm_runner-0.2.12-cp38-cp38-win_amd64.whl", "version": "0.2.12"}, {"date": "2025-05-09 09:47:57", "platforms": ["windows-x32"], "python_versions": ["3.8"], "sha256": "79b2bdaabd1196ee7a4304b899010b5bc3e09986f725393fca718edb1d5eae27", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/4b/7e/07e51568bbed9155feed18e3feb08ecb20a15d5ef75bc23db38731ea1e10/llm_runner-0.2.12-cp38-cp38-win32.whl", "version": "0.2.12"}, {"date": "2025-05-09 09:46:36", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "747ad0c2f376a7ad085ba6af47ceac45d2d33a77e162476c4324ab4ed328553a", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/6d/68/7f0e44be8bbfc0390eb6d166de603230e93ff5bb1e3b39e2c394a171ca79/llm_runner-0.2.12-cp38-cp38-macosx_10_12_x86_64.whl", "version": "0.2.12"}, {"date": "2025-05-09 09:46:31", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "cd4c3049fa7dd87aa4a39e7e4086bd1174914b2189542248541d078b9d1a3367", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/a3/12/3124043b0f889e617b2bde181a210d944913aba1d46b1ac5cbf9290bcf70/llm_runner-0.2.12-cp38-cp38-macosx_11_0_arm64.whl", "version": "0.2.12"}, {"date": "2025-05-09 09:46:21", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "ff47bd8c15dee000f81b157bb9baf21e43291a7aa3a5c7a4d7e6d7f58868c149", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/07/a0/dd8ec70e9f0178b66516f1c9c76de4ed5a339ad4d92886cffa7bd5edb909/llm_runner-0.2.12-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "version": "0.2.12"}, {"date": "2025-05-09 09:45:51", "platforms": ["linux-arm64"], "python_versions": ["3.8"], "sha256": "ff6c1d08f3c58ac1b556c4ac8b24f2add2a02842a6f384b7b12d92412ac65bc8", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/32/b7/18aec382b9e0d5c422b9e9deb05a1e1d95b73e7ff9e1b73b5a2ce02225af/llm_runner-0.2.12-cp38-cp38-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "version": "0.2.12"}]}, {"author": "rchl", "description": "Module with LSP-related utilities", "issues": "https://github.com/sublimelsp/lsp_utils/issues", "name": "lsp_utils", "releases": [{"date": "2024-10-28 13:55:57", "platforms": ["*"], "python_versions": ["3.8"], "sublime_text": ">=4070", "url": "https://codeload.github.com/sublimelsp/lsp_utils/zip/v3.2.2", "version": "3.2.2"}, {"date": "2023-10-14 22:32:24", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "3154 - 4069", "url": "https://codeload.github.com/sublimelsp/lsp_utils/zip/st3-v2.2.1", "version": "2.2.1"}]}, {"author": "lxml dev team", "description": "lxml", "issues": "https://bugs.launchpad.net/lxml", "name": "lxml", "releases": [{"date": "2025-06-26 16:27:24", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "f97487996a39cb18278ca33f7be98198f278d0bc3c5d0fd4d7b3d63646ca3c8a", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/63/27/868cdbda232595e2103f4bb97f77439fb558cfd309244873b9c87e4a2c43/lxml-6.0.0-cp38-cp38-win_amd64.whl", "version": "6.0.0"}, {"date": "2025-06-26 16:27:22", "platforms": ["windows-x32"], "python_versions": ["3.8"], "sha256": "185efc2fed89cdd97552585c624d3c908f0464090f4b91f7d92f8ed2f3b18f54", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/f3/83/97c03b02cc6827732cfe45d37867857e10cd529992c01817b2bd3b47619c/lxml-6.0.0-cp38-cp38-win32.whl", "version": "6.0.0"}, {"date": "2025-06-26 16:27:12", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "4eb114a0754fd00075c12648d991ec7a4357f9cb873042cc9a77bf3a7e30c9db", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/d4/3e/11c8423d567576e0387a3d30626dba998a3a01d1c400c9db6f0767b47e96/lxml-6.0.0-cp38-cp38-macosx_10_9_x86_64.whl", "version": "6.0.0"}, {"date": "2025-06-26 16:27:17", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "63b634facdfbad421d4b61c90735688465d4ab3a8853ac22c76ccac2baf98d97", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/a0/67/8f7ff46486462f439f092cb7a10e1f8da4e5f7916a372a8350513853f54b/lxml-6.0.0-cp38-cp38-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl", "version": "6.0.0"}, {"date": "2025-04-23 01:48:25", "platforms": ["linux-x32"], "python_versions": ["3.8"], "sha256": "2b31a3a77501d86d8ade128abb01082724c0dfd9524f542f2f07d693c9f1175f", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/a0/95/2a31efe19c8d6e4b7bdbcd1a9a6e0a2cb28472c9ea4c29b0e170c6bc7b08/lxml-5.4.0-cp38-cp38-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl", "version": "5.4.0"}, {"date": "2025-04-23 01:48:27", "platforms": ["linux-arm64"], "python_versions": ["3.8"], "sha256": "0e108352e203c7afd0eb91d782582f00a0b16a948d204d4dec8565024fafeea5", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/d8/f9/afc3d28beb7125b09bc54b5e7194b4e1bf9f75e8a0ba3f357adc1f6c6f86/lxml-5.4.0-cp38-cp38-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "version": "5.4.0"}, {"date": "2024-01-10 13:57:19", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "ae15347a88cf8af0949a9872b57a320d2605ae069bcdf047677318bc0bba45b1", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/32/b3/d52cdffe2417423f9e0ef72fbb998e5173bfe407a2c8418b042736c0a6f6/lxml-5.1.0-cp38-cp38-macosx_10_9_universal2.whl", "version": "5.1.0"}, {"date": "2019-01-03 07:01:21", "platforms": ["windows-x64"], "python_versions": ["3.3"], "sha256": "2b05e5e06f8e8c63595472dc887d0d6e0250af754a35ba690f6a6abf2ef85691", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/66/74/07282c6aca5a80257f929ed3875d701ae8b766fc3898f40711a0e4c9a06a/lxml-4.2.6-cp33-cp33m-win_amd64.whl", "version": "4.2.6"}, {"date": "2019-01-03 07:00:16", "platforms": ["windows-x32"], "python_versions": ["3.3"], "sha256": "f52fe795e08858192eea167290033b5ff24f50f51781cb78d989e8d63cfe73d1", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/0c/2a/5cb8e83b435102825077206306f02989177d9a2ba394c769e18753829de1/lxml-4.2.6-cp33-cp33m-win32.whl", "version": "4.2.6"}, {"date": "2018-03-21 21:53:40", "platforms": ["osx-x64"], "python_versions": ["3.3"], "sha256": "5b653c9379ce29ce271fbe1010c5396670f018e78b643e21beefbb3dc6d291de", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/bc/e4/7e576f5db0224c7aa656a7220c86c09707093264038901e0658ff000af2b/lxml-4.2.1-cp33-cp33m-macosx_10_6_x86_64.macosx_10_9_intel.macosx_10_9_x86_64.macosx_10_10_intel.macosx_10_10_x86_64.whl", "version": "4.2.1"}, {"date": "2018-03-21 21:32:48", "platforms": ["linux-x64"], "python_versions": ["3.3"], "sha256": "1b164bba1320b14905dcff77da10d5ce9c411ac4acc4fb4ed9a2a4d10fae38c9", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/15/aa/51beb9c422ac36f49c626f5ef9e47da03ca7eb15deb0ebe09d7535e46aab/lxml-4.2.1-cp33-cp33m-manylinux1_x86_64.whl", "version": "4.2.1"}, {"date": "2018-03-21 21:52:28", "platforms": ["linux-x32"], "python_versions": ["3.3"], "sha256": "3cf2830b9a6ad7f6e965fa53a768d4d2372a7856f20ffa6ce43d2fe9c0d34b19", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/8a/2f/29f9a6a15de405f35b538657b2ab3a74ef6d51824864b4a7ed3fdd98d148/lxml-4.2.1-cp33-cp33m-manylinux1_i686.whl", "version": "4.2.1"}]}, {"author": "facelessuser", "description": "Python Markdown module", "issues": "https://github.com/facelessuser/sublime-markdown/issues", "name": "<PERSON><PERSON>", "releases": [{"date": "2021-02-19 06:40:09", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/facelessuser/sublime-markdown/zip/3.2.2", "version": "3.2.2"}]}, {"author": "<PERSON><PERSON><PERSON>", "description": "MarkupSafe implements a text object that escapes characters so it is safe to use in HTML and XML.", "issues": "https://github.com/pallets/markupsafe/issues", "name": "MarkupSafe", "releases": [{"date": "2024-02-02 16:31:08", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "619bc166c4f2de5caa5a633b8b7326fbe98e0ccbfacabd87268a2b15ff73a029", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/92/21/357205f03514a49b293e214ac39de01fadd0970a6e05e4bf1ddd0ffd0881/MarkupSafe-2.1.5-cp38-cp38-win_amd64.whl", "version": "2.1.5"}, {"date": "2024-02-02 16:30:58", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "97cafb1f3cbcd3fd2b6fbfb99ae11cdb14deea0736fc2b0952ee177f2b813a46", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/4f/14/6f294b9c4f969d0c801a4615e221c1e084722ea6114ab2114189c5b8cbe0/MarkupSafe-2.1.5-cp38-cp38-macosx_10_9_x86_64.whl", "version": "2.1.5"}, {"date": "2024-02-02 16:30:57", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "656f7526c69fac7f600bd1f400991cc282b417d17539a1b228617081106feb4a", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/f8/ff/2c942a82c35a49df5de3a630ce0a8456ac2969691b230e530ac12314364c/MarkupSafe-2.1.5-cp38-cp38-macosx_10_9_universal2.whl", "version": "2.1.5"}, {"date": "2024-02-02 16:31:01", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "fa9db3f79de01457b03d4f01b34cf91bc0048eb2c3846ff26f66687c2f6d16ab", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/c7/bd/50319665ce81bb10e90d1cf76f9e1aa269ea6f7fa30ab4521f14d122a3df/MarkupSafe-2.1.5-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "version": "2.1.5"}, {"date": "2024-02-02 16:30:59", "platforms": ["linux-arm64"], "python_versions": ["3.8"], "sha256": "1f3fbcb7ef1f16e48246f704ab79d79da8a46891e2da03f8783a5b6fa41a9532", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/81/d4/fd74714ed30a1dedd0b82427c02fa4deec64f173831ec716da11c51a50aa/MarkupSafe-2.1.5-cp38-cp38-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "version": "2.1.5"}, {"date": "2019-07-11 15:36:33", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/MarkupSafe/zip/v1.1.1", "version": "1.1.1"}]}, {"author": "facelessuser", "description": "Markdown Popups for Sublime", "issues": "https://github.com/facelessuser/sublime-markdown-popups/issues", "name": "mdpopups", "releases": [{"date": "2022-07-23 16:56:01", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/facelessuser/sublime-markdown-popups/zip/4.2.2", "version": "4.2.2"}]}, {"author": "<PERSON>", "description": "More routines for operating on iterables, beyond itertools", "issues": "https://github.com/more-itertools/more-itertools/issues", "name": "more-itertools", "releases": [{"date": "2024-09-05 15:28:20", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "037b0d3203ce90cca8ab1defbbdac29d5f993fc20131f3664dc8d6acfa872aef", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/48/7e/3a64597054a70f7c86eb0a7d4fc315b8c1ab932f64883a297bdffeb5f967/more_itertools-10.5.0-py3-none-any.whl", "version": "10.5.0"}]}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "description": "Simple yet flexible natural sorting in Python.", "issues": "https://github.com/SethMMorton/natsort/issues", "name": "natsort", "releases": [{"date": "2023-06-20 04:17:17", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "4732914fb471f56b5cce04d7bae6f164a592c7712e1c85f9ef585e197299521c", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/ef/82/7a9d0550484a62c6da82858ee9419f3dd1ccc9aa1c26a1e43da3ecd20b0d/natsort-8.4.0-py3-none-any.whl", "version": "8.4.0"}, {"date": "2020-01-30 05:08:54", "platforms": ["*"], "python_versions": ["3.3"], "sha256": "daae7b2e22ef21305bf6921e49f6ad25d0e29f924e96e3bb447449e11446c726", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/4a/4e/ead3dd6b7c3b6011c04ed9e526b53a2e2b96f1205fb207f73374cb015810/natsort-6.2.1-py2.py3-none-any.whl", "version": "6.2.1"}]}, {"author": "wbond", "description": "Open a terminal to a specific folder and optionally set environment variables", "issues": "https://github.com/codexns/newterm/issues", "name": "newterm", "releases": [{"date": "2015-09-29 05:18:23", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/codexns/newterm/zip/1.0.0", "version": "1.0.0"}]}, {"author": "<PERSON><PERSON><PERSON>", "description": "NumPy is the fundamental package for scientific computing with Python.", "issues": "https://github.com/numpy/numpy/issues", "name": "numpy", "releases": [{"date": "2023-06-26 13:29:58", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "692f2e0f55794943c5bfff12b3f56f99af76f902fc47487bdfe97856de51a706", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/69/65/0d47953afa0ad569d12de5f65d964321c208492064c38fe3b0b9744f8d44/numpy-1.24.4-cp38-cp38-win_amd64.whl", "version": "1.24.4"}, {"date": "2023-06-26 13:27:49", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "1452241c290f3e2a312c137a9999cdbf63f78864d63c79039bda65ee86943f61", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/11/10/943cfb579f1a02909ff96464c69893b1d25be3731b5d3652c2e0cf1281ea/numpy-1.24.4-cp38-cp38-macosx_10_9_x86_64.whl", "version": "1.24.4"}, {"date": "2023-06-26 13:28:12", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "04640dab83f7c6c85abf9cd729c5b65f1ebd0ccf9de90b270cd61935eef0197f", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/a7/ae/f53b7b265fdc701e663fbb322a8e9d4b14d9cb7b2385f45ddfabfc4327e4/numpy-1.24.4-cp38-cp38-macosx_11_0_arm64.whl", "version": "1.24.4"}, {"date": "2023-06-26 13:29:09", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "dd80e219fd4c71fc3699fc1dadac5dcf4fd882bfc6f7ec53d30fa197b8ee22dc", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/98/5d/5738903efe0ecb73e51eb44feafba32bdba2081263d40c5043568ff60faf/numpy-1.24.4-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "version": "1.24.4"}, {"date": "2023-06-26 13:28:35", "platforms": ["linux-arm64"], "python_versions": ["3.8"], "sha256": "a5425b114831d1e77e4b5d812b69d11d962e104095a5b9c3b641a218abcc050e", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/25/6f/2586a50ad72e8dbb1d8381f837008a0321a3516dfd7cb57fc8cf7e4bb06b/numpy-1.24.4-cp38-cp38-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "version": "1.24.4"}, {"date": "2016-03-27 21:04:06", "platforms": ["osx-x64"], "python_versions": ["3.3"], "sha256": "07bd13135c267bc5c449c196c428935b0880cb43579af1d0da883e9dadc0ef64", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/26/ac/38141d8c0ca181031f8335d6febf9615407c02edf577b5946374da9bf377/numpy-1.11.0-cp33-cp33m-macosx_10_6_intel.macosx_10_9_intel.macosx_10_9_x86_64.macosx_10_10_intel.macosx_10_10_x86_64.whl", "version": "1.11.0"}, {"date": "2016-04-20 01:35:03", "platforms": ["linux-x64"], "python_versions": ["3.3"], "sha256": "34e5f4a13c308dc67e60eb3c27a5e79c2599e62eaa9b30b7fa4833c9048a3ec2", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/5b/49/f3f3212c952467eec3b3299618c5ece48e7c67827bd7f236b41c4e50c9e1/numpy-1.10.4-cp33-cp33m-manylinux1_x86_64.whl", "version": "1.10.4"}]}, {"author": "csch0", "description": "Python oauthlib module", "issues": "https://github.com/packagecontrol/oauthlib/issues", "name": "<PERSON><PERSON><PERSON><PERSON>", "releases": [{"date": "2015-03-02 06:29:58", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/oauthlib/zip/v0.7.2", "version": "0.7.2"}]}, {"author": "ijl", "description": "orjson is a fast, correct JSON library for Python.", "issues": "https://github.com/ijl/orjson/issues", "name": "<PERSON><PERSON><PERSON>", "releases": [{"date": "2025-01-18 15:55:03", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "5dd9ef1639878cc3efffed349543cbf9372bdbd79f478615a1c633fe4e4180d1", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/72/78/11d6afa317d3c7ee3c35b3a70e91776bf60ea9f010b629cc40d4a00edde8/orjson-3.10.15-cp38-cp38-win_amd64.whl", "version": "3.10.15"}, {"date": "2025-01-18 15:55:01", "platforms": ["windows-x32"], "python_versions": ["3.8"], "sha256": "305b38b2b8f8083cc3d618927d7f424349afce5975b316d33075ef0f73576b60", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/13/1f/7f01afc23a7dee108baa31fbaa8e8d3f6b56b915201bf079c9586b37680a/orjson-3.10.15-cp38-cp38-win32.whl", "version": "3.10.15"}, {"date": "2025-01-18 15:54:44", "platforms": ["osx-arm64", "osx-x64"], "python_versions": ["3.8"], "sha256": "5e8afd6200e12771467a1a44e5ad780614b86abb4b11862ec54861a82d677746", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/e8/93/7e826e2fe347bba393c60c3554a6966c09dc17613d7af2b6686348171ba9/orjson-3.10.15-cp38-cp38-macosx_10_15_x86_64.macosx_11_0_arm64.macosx_10_15_universal2.whl", "version": "3.10.15"}, {"date": "2025-01-18 18:12:09", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "7c864a80a2d467d7786274fce0e4f93ef2a7ca4ff31f7fc5634225aaa4e9e98c", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/72/3c/2e26157d69d127c5663cdaa53a31860ca0df0a9a89ece81c81800ef99490/orjson-3.10.15-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "version": "3.10.15"}, {"date": "2025-01-18 18:12:06", "platforms": ["linux-arm64"], "python_versions": ["3.8"], "sha256": "da9a18c500f19273e9e104cca8c1f0b40a6470bcccfc33afcc088045d0bf5ea6", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/6e/71/2d31ebc2f2da9249ce77dea6c31f2a7df2735fe6ec9a326096cbcc0448e9/orjson-3.10.15-cp38-cp38-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "version": "3.10.15"}]}, {"author": "wbond", "description": "Allows Sublime Text packages to emit and listen for events", "issues": "https://github.com/codexns/package_events/issues", "name": "package_events", "releases": [{"date": "2015-09-29 12:29:58", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/codexns/package_events/zip/1.0.1", "version": "1.0.1"}]}, {"author": "math2001", "description": "Allow final user to disable key bindings with very few changes needed from the dev", "issues": "https://github.com/math2001/package_setting_context/issues", "name": "package_setting_context", "releases": [{"date": "2017-04-09 23:21:03", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/math2001/package_setting_context/zip/v1.0.2", "version": "1.0.2"}]}, {"author": "j<PERSON><PERSON><PERSON>", "description": "Python implementation of the SSHv2 protocol - http://paramiko-www.readthedocs.org/en/latest/index.html", "issues": "https://github.com/jlegewie/sublime-paramiko/issues", "name": "<PERSON><PERSON><PERSON>", "releases": [{"date": "2019-12-20 03:29:44", "platforms": ["osx"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/jlegewie/sublime-paramiko/zip/v1.18.5", "version": "1.18.5"}]}, {"author": "<PERSON>", "description": "Parso is a Python parser that supports error recovery and round-trip parsing for different Python versions (in multiple Python versions).", "issues": "https://github.com/davidhalter/parso/issues", "name": "parso", "releases": [{"date": "2024-04-05 09:43:53", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "a418670a20291dacd2dddc80c377c5c3791378ee1e8d12bffc35420643d43f18", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/c6/ac/dac4a63f978e4dcb3c6d3a78c4d8e0192a113d288502a1216950c41b1027/parso-0.8.4-py2.py3-none-any.whl", "version": "0.8.4"}]}, {"author": "FichteFoll", "description": "Python pathlib module", "issues": "https://github.com/packagecontrol/pathlib/issues", "name": "pathlib", "releases": [{"date": "2018-01-25 02:09:07", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/pathlib/zip/v1.0.1", "version": "1.0.1"}]}, {"author": "vovkkk", "description": "Path utilities for Python https://pypi.python.org/pypi/pathtools", "issues": "https://github.com/vovkkk/sublime-pathtools/issues", "name": "pathtools", "releases": [{"date": "2016-07-06 12:45:52", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/vovkkk/sublime-pathtools/zip/0.1.2", "version": "0.1.2"}]}, {"author": "varp", "description": "Python pexpect module", "issues": "https://github.com/varp/sublime-pexpect/issues", "name": "pexpect", "releases": [{"date": "2024-10-09 10:34:06", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/varp/sublime-pexpect/zip/v4.9.0", "version": "4.9.0"}]}, {"author": " <PERSON>", "description": "Pillow is the friendly PIL fork by <PERSON> and contributors. PIL is the Python Imaging Library by <PERSON><PERSON> and contributors.", "issues": "https://github.com/python-pillow/Pillow/issues", "name": "pillow", "releases": [{"date": "2024-07-01 09:47:34", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "5161eef006d335e46895297f642341111945e2c1c899eb406882a6c61a4357ab", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/f2/75/3cb820b2812405fc7feb3d0deb701ef0c3de93dc02597115e00704591bc9/pillow-10.4.0-cp38-cp38-win_amd64.whl", "version": "10.4.0"}, {"date": "2024-07-01 09:47:32", "platforms": ["windows-x32"], "python_versions": ["3.8"], "sha256": "e88d5e6ad0d026fba7bdab8c3f225a69f063f116462c49892b0149e21b6c0a0e", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/69/66/03002cb5b2c27bb519cba63b9f9aa3709c6f7a5d3b285406c01f03fb77e5/pillow-10.4.0-cp38-cp38-win32.whl", "version": "10.4.0"}, {"date": "2024-07-01 09:47:11", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "8d4d5063501b6dd4024b8ac2f04962d661222d120381272deea52e3fc52d3736", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/56/70/f40009702a477ce87d8d9faaa4de51d6562b3445d7a314accd06e4ffb01d/pillow-10.4.0-cp38-cp38-macosx_10_10_x86_64.whl", "version": "10.4.0"}, {"date": "2024-07-01 09:47:14", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "7c1ee6f42250df403c5f103cbd2768a28fe1a0ea1f0f03fe151c8741e1469c8b", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/10/43/105823d233c5e5d31cea13428f4474ded9d961652307800979a59d6a4276/pillow-10.4.0-cp38-cp38-macosx_11_0_arm64.whl", "version": "10.4.0"}, {"date": "2024-07-01 09:47:19", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "7a8d4bade9952ea9a77d0c3e49cbd8b2890a399422258a77f357b9cc9be8d680", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/84/4c/69bbed9e436ac22f9ed193a2b64f64d68fcfbc9f4106249dc7ed4889907b/pillow-10.4.0-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "version": "10.4.0"}, {"date": "2024-07-01 09:47:16", "platforms": ["linux-arm64"], "python_versions": ["3.8"], "sha256": "b15e02e9bb4c21e39876698abf233c8c579127986f8207200bc8a8f6bb27acf2", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/3c/ad/7850c10bac468a20c918f6a5dbba9ecd106ea1cdc5db3c35e33a60570408/pillow-10.4.0-cp38-cp38-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "version": "10.4.0"}]}, {"author": "evandrocoan", "description": "Python interface to a plantuml web service instead of having to run Java locally", "issues": "https://github.com/evandrocoan/plantumlconnection/issues", "name": "plantumlconnection", "releases": [{"date": "2020-11-21 18:25:36", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/evandrocoan/plantumlconnection/zip/3999", "version": "3999"}]}, {"author": "evandrocoan", "description": "An extended version of portalocker to lock files in Python using the with statement", "issues": "https://github.com/evandroforks/portalockerfile/issues", "name": "portalockerfile", "releases": [{"date": "2018-12-17 03:16:15", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/evandroforks/portalockerfile/zip/3999", "version": "3999"}]}, {"author": "randy3k", "description": "Python psutil module", "issues": "https://github.com/packagecontrol/psutil/issues", "name": "psutil", "releases": [{"date": "2024-06-18 21:41:57", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "ffe7fc9b6b36beadc8c322f84e1caff51e8703b88eee1da46d1e3a6ae11b4fd0", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/7c/06/63872a64c312a24fb9b4af123ee7007a306617da63ff13bcc1432386ead7/psutil-6.0.0-cp38-abi3-macosx_11_0_arm64.whl", "version": "6.0.0"}, {"date": "2022-10-18 20:14:48", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "35feafe232d1aaf35d51bd42790cbccb882456f9f18cdc411532902370d660df", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/69/cf/47a028bbb4589fdc0494bc60f134c73e319ec78c86c37e2dc66fd118e4db/psutil-5.9.3-cp38-cp38-win_amd64.whl", "version": "5.9.3"}, {"date": "2022-10-18 20:14:45", "platforms": ["windows-x32"], "python_versions": ["3.8"], "sha256": "6ced1ad823ecfa7d3ce26fe8aa4996e2e53fb49b7fed8ad81c80958501ec0619", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/69/3d/e1a12f505eb0171912b94e4689453639bb0deeb70ab4eddbc7b9266f819e/psutil-5.9.3-cp38-cp38-win32.whl", "version": "5.9.3"}, {"date": "2022-10-18 20:14:30", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "ba38cf9984d5462b506e239cf4bc24e84ead4b1d71a3be35e66dad0d13ded7c1", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/01/d6/9ca99b416dddf4a49855a9ebf4af3a2db9526e94e9693da169fa5ed61788/psutil-5.9.3-cp38-cp38-macosx_10_9_x86_64.whl", "version": "5.9.3"}, {"date": "2022-10-18 20:14:41", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "8a4e07611997acf178ad13b842377e3d8e9d0a5bac43ece9bfc22a96735d9a4f", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/af/5d/9c03a47af929fc12699fcf5174313744eef33a7b9e106e8111f57427b7d7/psutil-5.9.3-cp38-cp38-manylinux_2_12_x86_64.manylinux2010_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "version": "5.9.3"}, {"date": "2019-12-10 08:04:35", "platforms": ["osx-x64", "windows", "linux"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/psutil/zip/v5.6.7", "version": "5.6.7"}]}, {"author": "randy3k", "description": "Python ptyprocess module", "issues": "https://github.com/packagecontrol/ptyprocess/issues", "name": "ptyprocess", "releases": [{"date": "2024-12-02 01:00:46", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/ptyprocess/zip/v0.7.0", "version": "0.7.0"}, {"date": "2020-12-28 15:15:28", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "4b41f3967fce3af57cc7e94b888626c18bf37a083e3651ca8feeb66d492fef35", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/22/a6/858897256d0deac81a172289110f31629fc4cee19b6f01283303e18c8db3/ptyprocess-0.7.0-py2.py3-none-any.whl", "version": "0.7.0"}]}, {"author": "<PERSON>", "description": "Complete C99 parser in pure Python", "issues": "https://github.com/eliben/pycparser/issues", "name": "pyc<PERSON><PERSON>", "releases": [{"date": "2024-03-30 13:22:20", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "c3702b6d3dd8c7abc1afa565d7e63d53a1d0bd86cdc24edd75470f4de499cfcc", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/13/a3/a812df4e2dd5696d1f351d58b8fe16a405b234ad2886a0dab9183fb78109/pycparser-2.22-py3-none-any.whl", "version": "2.22"}]}, {"author": "j<PERSON><PERSON><PERSON>", "description": "Python Cryptography Toolkit - https://www.dlitz.net/software/pycrypto/", "issues": "https://github.com/jlegewie/sublime-PyCrypto/issues", "name": "PyCrypto", "releases": [{"date": "2015-04-24 18:59:52", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/jlegewie/sublime-PyCrypto/zip/v1.0.0", "version": "1.0.0"}]}, {"author": "samuel<PERSON><PERSON>", "description": "Data validation using Python type hints.", "issues": "https://github.com/pydantic/pydantic/issues", "name": "pydantic", "releases": [{"date": "2025-01-24 01:42:10", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "427d664bf0b8a2b34ff5dd0f5a18df00591adcee7198fbd71981054cef37b584", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/f4/3c/8cc1cc84deffa6e25d2d0c688ebb80635dfdbf1dbea3e30c541c8cf4d860/pydantic-2.10.6-py3-none-any.whl", "version": "2.10.6"}]}, {"author": "samuel<PERSON><PERSON>", "description": "Core functionality for Pydantic validation and serialization.", "issues": "https://github.com/pydantic/pydantic-core/issues", "name": "pydantic-core", "releases": [{"date": "2024-12-18 11:30:14", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "fd1aea04935a508f62e0d0ef1f5ae968774a32afc306fb8545e06f5ff5cdf3ad", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/aa/6d/df49c17f024dfc58db0bacc7b03610058018dd2ea2eaf748ccbada4c3d06/pydantic_core-2.27.2-cp38-cp38-win_amd64.whl", "version": "2.27.2"}, {"date": "2024-12-18 11:30:11", "platforms": ["windows-x32"], "python_versions": ["3.8"], "sha256": "f6f8e111843bbb0dee4cb6594cdc73e79b3329b526037ec242a3e49012495b3b", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/8a/2b/e1059506795104349712fbca647b18b3f4a7fd541c099e6259717441e1e0/pydantic_core-2.27.2-cp38-cp38-win32.whl", "version": "2.27.2"}, {"date": "2024-12-18 11:29:40", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "d3e8d504bdd3f10835468f29008d72fc8359d95c9c415ce6e767203db6127506", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/43/53/13e9917fc69c0a4aea06fd63ed6a8d6cda9cf140ca9584d49c1650b0ef5e/pydantic_core-2.27.2-cp38-cp38-macosx_10_12_x86_64.whl", "version": "2.27.2"}, {"date": "2024-12-18 11:29:44", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "521eb9b7f036c9b6187f0b47318ab0d7ca14bd87f776240b90b21c1f4f149320", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/f4/20/26c549249769ed84877f862f7bb93f89a6ee08b4bee1ed8781616b7fbb5e/pydantic_core-2.27.2-cp38-cp38-macosx_11_0_arm64.whl", "version": "2.27.2"}, {"date": "2024-12-18 11:29:57", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "9fdbe7629b996647b99c01b37f11170a57ae675375b14b8c13b8518b8320ced5", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/f7/a3/5f19bc495793546825ab160e530330c2afcee2281c02b5ffafd0b32ac05e/pydantic_core-2.27.2-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "version": "2.27.2"}, {"date": "2024-12-18 11:29:46", "platforms": ["linux-arm64"], "python_versions": ["3.8"], "sha256": "85210c4d99a0114f5a9481b44560d7d1e35e32cc5634c656bc48e590b669b145", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/35/eb/8234e05452d92d2b102ffa1b56d801c3567e628fdc63f02080fdfc68fd5e/pydantic_core-2.27.2-cp38-cp38-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "version": "2.27.2"}]}, {"author": "fopina", "description": " Python FIS MTM/PIP SQL/RPC Interface", "issues": "https://github.com/fopina/sublime-pyfispip/issues", "name": "pyfisp<PERSON>", "releases": [{"date": "2017-03-13 19:23:20", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/fopina/sublime-pyfispip/zip/v0.0.3", "version": "0.0.3"}]}, {"author": "AndreasBackx", "description": "Python pygments module", "issues": "https://github.com/packagecontrol/pygments/issues", "name": "pygments", "releases": [{"date": "2015-03-16 02:36:40", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/pygments/zip/v2.0.2", "version": "2.0.2"}]}, {"author": "facelessuser", "description": "PyMdown Extensions for Python Markdown", "issues": "https://github.com/facelessuser/sublime-pymdownx/issues", "name": "pymdownx", "releases": [{"date": "2021-02-19 06:38:15", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/facelessuser/sublime-pymdownx/zip/8.1.1", "version": "8.1.1"}]}, {"author": "pyserial", "description": "Python serial port access library", "issues": "https://github.com/bisguzar/sublime-serial/issues", "name": "pyserial", "releases": [{"date": "2018-05-29 10:24:45", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/bisguzar/sublime-serial/zip/1.0.0", "version": "1.0.0"}]}, {"author": "<PERSON>", "description": "Python bindings for the simdjson project, a SIMD-accelerated JSON parser.", "issues": "https://github.com/TkTech/pysimdjson/issues", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "releases": [{"date": "2022-09-12 18:40:31", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "b35075e02781d730699b363a47d843fd63d42a6304977648562bb56635cd0b6f", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/c5/26/e0fd0ed3d5e8040a5d8c37a17f4123adbdb4c013cd0df4b2b75526fdad83/pysimdjson-5.0.2-cp38-cp38-win_amd64.whl", "version": "5.0.2"}, {"date": "2022-09-12 18:40:17", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "5c79bc86d7efff5f195b5823ec5f7d5d52383c9678c764e9d80bcd3c42102a49", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/fb/40/cfdf6bc853d7e74fb433202a06bf3da5aee14f56c1d56c23988348476158/pysimdjson-5.0.2-cp38-cp38-macosx_10_9_x86_64.whl", "version": "5.0.2"}, {"date": "2022-09-12 18:40:16", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "1731d830eda3f20ea37700c7401e25226bc264bc3529284fd29e1d13d9ba3412", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/bd/13/69de19ce0d070992f5c39ea1566e067f606ae344a7685e41000301bc0fa9/pysimdjson-5.0.2-cp38-cp38-macosx_10_9_universal2.whl", "version": "5.0.2"}, {"date": "2022-09-12 18:40:21", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "da11344c5ae72a00460b6de0fd297108dda0ab72a421b979257d075cc3e9aa91", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/df/27/e38b2c83fd686da90fddcc75bbd206b3dbfb0b4f2e01026fb91e1973434f/pysimdjson-5.0.2-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "version": "5.0.2"}, {"date": "2022-09-12 18:40:19", "platforms": ["linux-arm64"], "python_versions": ["3.8"], "sha256": "956702ba73d3094808fd82eef13981f95481aff39a0b2ba1f97681dfe024d8aa", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/f8/45/c13f7ac2c6a4dc51db47c1beb08ddb795b1267ed95756c70c5e89640e619/pysimdjson-5.0.2-cp38-cp38-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "version": "5.0.2"}]}, {"author": "<PERSON><PERSON>", "description": "An in memory VTXXX-compatible terminal emulator", "issues": "https://github.com/selectel/pyte/issues", "name": "pyte", "releases": [{"date": "2023-11-12 09:33:41", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "85db42a35798a5aafa96ac4d8da78b090b2c933248819157fc0e6f78876a0135", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/59/d0/bb522283b90853afbf506cd5b71c650cf708829914efd0003d615cf426cd/pyte-0.8.2-py3-none-any.whl", "version": "0.8.2"}, {"date": "2022-06-07 19:22:02", "platforms": ["*"], "python_versions": ["3.3"], "sha256": "dd16d25e4cd27642cbbe94f9e8eaa1c59b9389ce0943e971dd4786f4e52daee0", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/d7/fa/f1c65cf79f3923569629e96134e6f648be9b866d81710a80ff7465d8a420/pyte-0.8.0-py2.py3-none-any.whl", "version": "0.8.0"}]}, {"author": "Python OpenXML Team", "description": "Create, read, and update Microsoft Word .docx files.", "issues": "https://github.com/python-openxml/python-docx/issues", "name": "python-docx", "releases": [{"date": "2024-05-01 19:41:47", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "08c20d6058916fb19853fcf080f7f42b6270d89eac9fa5f8c15f691c0017fabe", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/3e/3d/330d9efbdb816d3f60bf2ad92f05e1708e4a1b9abe80461ac3444c83f749/python_docx-1.1.2-py3-none-any.whl", "version": "1.1.2"}, {"date": "2024-02-26 20:01:40", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/writeml/sublime-docx/zip/0.8.11", "version": "0.8.11"}]}, {"author": "FichteFoll", "description": "Python pytz module", "issues": "https://github.com/packagecontrol/pytz/issues", "name": "pytz", "releases": [{"date": "2025-03-25 02:24:58", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sha256": "5ddf76296dd8c44c26eb8f4b6f35488f3ccbf6fbbd7adee0b7262d43f0ec2f00", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/81/c4/34e93fe5f5429d7570ec1fa436f1986fb1f00c3e0f43a589fe2bbcd22c3f/pytz-2025.2-py2.py3-none-any.whl", "version": "2025.2"}]}, {"author": "randy3k", "description": "Pywin32 module", "issues": "https://github.com/packagecontrol/pywin32/issues", "name": "pywin32", "releases": [{"date": "2025-07-14 20:12:57", "platforms": ["windows"], "python_versions": ["3.8"], "sha256": "c8015b09fb9a5e188f83b7b04de91ddca4658cee2ae6f3bc483f0b21a77ef6cd", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/ff/6c/94c10268bae5d0d0c6509bdfb5aa08882d11a9ccdf89ff1cde59a6161afb/pywin32-311-cp38-cp38-win_amd64.whl", "version": "311"}, {"date": "2016-07-20 14:45:09", "platforms": ["windows-x32", "windows-x64"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/pywin32/zip/v0.0.220", "version": "0.0.220"}]}, {"author": "randy3k", "description": "Python winpty module", "issues": "https://github.com/packagecontrol/pywinpty/issues", "name": "py<PERSON><PERSON>y", "releases": [{"date": "2019-12-12 04:58:55", "platforms": ["windows-x32", "windows-x64"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/pywinpty/zip/v0.5.8-dev0", "version": "0.5.8-dev0"}, {"date": "2019-11-27 03:33:13", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "8fc5019ff3efb4f13708bd3b5ad327589c1a554cb516d792527361525a7cb78c", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/f8/9f/cb2a929f18cd2a3cf6242cf57b4d723ecf45b97653795475825d6af218a4/pywinpty-0.5.7-cp38-cp38-win_amd64.whl", "version": "0.5.7"}, {"date": "2019-12-10 07:30:58", "platforms": ["windows-x32", "windows-x64"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/pywinpty/zip/v0.5.7", "version": "0.5.7"}]}, {"author": "FichteFoll", "description": "Python PyYAML module", "issues": "https://github.com/packagecontrol/pyyaml/issues", "name": "pyyaml", "releases": [{"date": "2019-07-11 14:51:34", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/pyyaml/zip/v5.1.1", "version": "5.1.1"}]}, {"author": "randy3k", "description": "Python ZMQ module", "issues": "https://github.com/packagecontrol/pyzmq/issues", "name": "pyzmq", "releases": [{"date": "2025-06-13 14:08:15", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "55a0155b148fe0428285a30922f7213539aa84329a5ad828bca4bbbc665c70a4", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/b7/11/20bbfcc6395d5f2f5247aa88fef477f907f8139913666aec2a17af7ccaf1/pyzmq-27.0.0-cp38-cp38-win_amd64.whl", "version": "27.0.0"}, {"date": "2025-06-13 14:08:14", "platforms": ["windows-x32"], "python_versions": ["3.8"], "sha256": "26b72c5ae20bf59061c3570db835edb81d1e0706ff141747055591c4b41193f8", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/a0/fa/967b2427bb0cadcc3a1530db2f88dfbfd46d781df2a386a096d7524df6cf/pyzmq-27.0.0-cp38-cp38-win32.whl", "version": "27.0.0"}, {"date": "2025-06-13 14:08:02", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "f4162dbbd9c5c84fb930a36f290b08c93e35fce020d768a16fc8891a2f72bab8", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/f2/b3/22246a851440818b0d3e090374dcfa946df05d1a6aa04753c1766c658731/pyzmq-27.0.0-cp38-cp38-macosx_10_15_universal2.whl", "version": "27.0.0"}, {"date": "2025-06-13 14:08:07", "platforms": ["linux-arm64"], "python_versions": ["3.8"], "sha256": "6e435540fa1da54667f0026cf1e8407fe6d8a11f1010b7f06b0b17214ebfcf5e", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/5a/19/dbff1b4a6aca1a83b0840f84c3ae926a19c0771b54e18a89683e1f0f74f0/pyzmq-27.0.0-cp38-cp38-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl", "version": "27.0.0"}, {"date": "2025-04-04 12:04:35", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "445c97854204119ae2232503585ebb4fa7517142f71092cb129e5ee547957a1f", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/c6/68/6f901c9e8b5712957a8c8dbf17a53fc8580aa7d47c6337db465afd780abc/pyzmq-26.4.0-cp38-cp38-manylinux_2_12_x86_64.manylinux2010_x86_64.whl", "version": "26.4.0"}, {"date": "2025-04-04 12:04:33", "platforms": ["linux-x32"], "python_versions": ["3.8"], "sha256": "51d18be6193c25bd229524cfac21e39887c8d5e0217b1857998dfbef57c070a4", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/31/7d/85d2c99a248b55bcfcb3ec0c7a3175a5ddfb42032a7cd2b36025d8fe055f/pyzmq-26.4.0-cp38-cp38-manylinux_2_12_i686.manylinux2010_i686.whl", "version": "26.4.0"}, {"date": "2025-01-30 11:40:22", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "1c6ae0e95d0a4b0cfe30f648a18e764352d5415279bdf34424decb33e79935b8", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/cd/74/b77cab27649917e8a7304c0a4b57f138a092177106a680cccc541189af40/pyzmq-26.2.1-cp38-cp38-macosx_10_9_x86_64.whl", "version": "26.2.1"}, {"date": "2019-12-10 18:51:20", "platforms": ["osx-x64", "linux-x32", "linux-x64", "windows-x32", "windows-x64"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/pyzmq/zip/18.1.1", "version": "18.1.1"}]}, {"author": "<PERSON>", "description": "Alternative regular expression module, to replace re.", "issues": "https://github.com/mrabarnett/mrab-regex/issues", "name": "regex", "releases": [{"date": "2024-11-06 20:11:50", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "bb8f74f2f10dbf13a0be8de623ba4f9491faf58c24064f32b65679b021ed0001", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/cf/69/c39e16320400842eb4358c982ef5fc680800866f35ebfd4dd38a22967ce0/regex-2024.11.6-cp38-cp38-win_amd64.whl", "version": "2024.11.6"}, {"date": "2024-11-06 20:11:48", "platforms": ["windows-x32"], "python_versions": ["3.8"], "sha256": "6f44ec28b1f858c98d3036ad5d7d0bfc568bdd7a74f9c24e25f41ef1ebfd81a4", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/cc/06/c817c9201f09b7d9dd033039ba90d8197c91e9fe2984141f2d1de270c159/regex-2024.11.6-cp38-cp38-win32.whl", "version": "2024.11.6"}, {"date": "2024-11-06 20:11:18", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "ad182d02e40de7459b73155deb8996bbd8e96852267879396fb274e8700190e3", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/5a/5a/586bafa294c5d2451265d3685815606c61e620f469cac3b946fff0a4aa48/regex-2024.11.6-cp38-cp38-macosx_10_9_x86_64.whl", "version": "2024.11.6"}, {"date": "2024-11-06 20:11:16", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "3a51ccc315653ba012774efca4f23d1d2a8a8f278a6072e29c7147eee7da446b", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/44/0f/207b37e6e08d548fac0aa00bf0b7464126315d58ab5161216b8cb3abb2aa/regex-2024.11.6-cp38-cp38-macosx_10_9_universal2.whl", "version": "2024.11.6"}, {"date": "2024-11-06 20:11:29", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "d1cee317bfc014c2419a76bcc87f071405e3966da434e03e13beb45f8aced1a6", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/5a/c8/dc7153ceb5bcc344f5c4f0291ea45925a5f00009afa3849e91561ac2e847/regex-2024.11.6-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "version": "2024.11.6"}, {"date": "2024-11-06 20:11:32", "platforms": ["linux-x32"], "python_versions": ["3.8"], "sha256": "50153825ee016b91549962f970d6a4442fa106832e14c918acd1c8e479916c4f", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/2a/29/841489ea52013062b22625fbaf49b0916aeb62bae2e56425ac30f9dead46/regex-2024.11.6-cp38-cp38-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", "version": "2024.11.6"}, {"date": "2024-11-06 20:11:23", "platforms": ["linux-arm64"], "python_versions": ["3.8"], "sha256": "40291b1b89ca6ad8d3f2b82782cc33807f1406cf68c8d440861da6304d8ffbbd", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/0a/27/0b3cf7d9fbe43301aa3473d54406019a7380abe4e3c9ae250bac13c4fdb3/regex-2024.11.6-cp38-cp38-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "version": "2024.11.6"}, {"date": "2017-10-08 21:39:29", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/facelessuser/sublime-regex/zip/1.3.1", "version": "1.3.1"}]}, {"author": "<PERSON>", "description": "Python HTTP for Humans.", "issues": "https://github.com/psf/requests/issues", "name": "requests", "releases": [{"date": "2025-06-09 16:43:05", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "27babd3cda2a6d50b30443204ee89830707d396671944c998b5975b031ac2b2c", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/7c/e4/56027c4a6b4ae70ca9de302488c5ca95ad4a39e190093d6c1a8ace08341b/requests-2.32.4-py3-none-any.whl", "version": "2.32.4"}, {"date": "2018-11-02 06:43:08", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/requests/zip/2.15.1", "version": "2.15.1"}]}, {"author": "<PERSON>", "description": "OAuthlib authentication support for Requests.", "issues": "https://github.com/requests/requests-oauthlib/issues", "name": "requests-o<PERSON><PERSON><PERSON>", "releases": [{"date": "2024-03-22 20:32:28", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "7dd8a5c40426b779b0868c404bdef9768deccf22749cde15852df527e6269b36", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/3b/5d/63d4ae3b9daea098d5d6f5da83984853c1bbacd5dc826764b249fe119d24/requests_oauthlib-2.0.0-py2.py3-none-any.whl", "version": "2.0.0"}, {"date": "2015-03-02 06:12:57", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/requests-oauthlib/zip/v0.4.2", "version": "0.4.2"}]}, {"author": "FichteFoll", "description": "Python resumeback module", "issues": "https://github.com/packagecontrol/resumeback/issues", "name": "resumeback", "releases": [{"date": "2020-04-10 01:18:12", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/resumeback/zip/v1.0.0", "version": "1.0.0"}]}, {"author": "<PERSON>", "description": "Python ruamel.yaml module", "issues": "https://github.com/Thom1729/sublime-ruamel/issues", "name": "ruamel.yaml", "releases": [{"date": "2018-12-21 20:09:21", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/Thom1729/sublime-ruamel/zip/v1.2.0", "version": "1.2.0"}, {"date": "2025-06-09 08:51:06", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "710ff198bb53da66718c7db27eec4fbcc9aa6ca7204e4c1df2f282b6fe5eb6b2", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/af/6d/6fe4805235e193aad4aaf979160dd1f3c487c57d48b810c816e6e842171b/ruamel.yaml-0.18.14-py3-none-any.whl", "version": "0.18.14"}]}, {"author": "<PERSON><PERSON><PERSON>", "description": "C version of reader, parser and emitter for ruamel.yaml derived from libyaml", "issues": "https://sourceforge.net/p/ruamel-yaml-clib/tickets/", "name": "ruamel.yaml.clib", "releases": [{"date": "2023-10-03 18:13:35", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "56f4252222c067b4ce51ae12cbac231bce32aee1d33fbfc9d17e5b8d6966c312", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/24/ce/6f587283caaff93d0b9cac2f244fcda686897e83401bb1aa91803db7bf94/ruamel.yaml.clib-0.2.8-cp38-cp38-win_amd64.whl", "version": "0.2.8"}, {"date": "2023-10-03 18:13:32", "platforms": ["windows-x32"], "python_versions": ["3.8"], "sha256": "955eae71ac26c1ab35924203fda6220f84dce57d6d7884f189743e2abe3a9fbe", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/1d/fe/a638c3ad6e74f4b15c8c1aa7de61a0cfe58c629d48ea59cf07dce5eaee1e/ruamel.yaml.clib-0.2.8-cp38-cp38-win32.whl", "version": "0.2.8"}, {"date": "2023-10-03 18:13:27", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "1b617618914cb00bf5c34d4357c37aa15183fa229b24767259657746c9077615", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/18/52/8dc27bbd9ef1d4695975b8dc132c27c431d0186037ad3c731a6dd1c154b9/ruamel.yaml.clib-0.2.8-cp38-cp38-macosx_10_9_x86_64.whl", "version": "0.2.8"}, {"date": "2023-10-03 18:13:28", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "a6a9ffd280b71ad062eae53ac1659ad86a17f59a0fdc7699fd9be40525153337", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/08/4c/5770b8f318fe404a455141a7a33a5568c27a1f944724e82354c8f3554db2/ruamel.yaml.clib-0.2.8-cp38-cp38-macosx_12_0_arm64.whl", "version": "0.2.8"}, {"date": "2023-10-03 18:13:30", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "700e4ebb569e59e16a976857c8798aee258dceac7c7d6b50cab63e080058df91", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/22/fa/b2a8fd49c92693e9b9b6b11eef4c2a8aedaca2b521ab3e020aa4778efc23/ruamel.yaml.clib-0.2.8-cp38-cp38-manylinux_2_5_x86_64.manylinux1_x86_64.whl", "version": "0.2.8"}, {"date": "2023-11-09 07:40:24", "platforms": ["linux-arm64"], "python_versions": ["3.8"], "sha256": "305889baa4043a09e5b76f8e2a51d4ffba44259f6b4c72dec8ca56207d9c6fe1", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/5a/45/644d839c09c0717c2d7f26b705560ad74b3056085b3bc7f9c2ac2081317b/ruamel.yaml.clib-0.2.8-cp38-cp38-manylinux_2_24_aarch64.whl", "version": "0.2.8"}]}, {"author": "guil<PERSON><PERSON><PERSON>", "description": "Reactive extensions for Python", "issues": "https://github.com/guillermooo/rxpyst/issues", "name": "rx", "releases": [{"date": "2016-06-13 16:57:47", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/guillermooo/rxpyst/zip/0.1.1", "version": "0.1.1"}]}, {"author": "blitzrk", "description": "Sassc binaries", "issues": "https://github.com/blitzrk/sublime_sassc/issues", "name": "sassc", "releases": [{"date": "2016-04-25 23:55:52", "platforms": ["osx-x64", "linux-x32", "linux-x64", "windows-x32", "windows-x64"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/blitzrk/sublime_sassc/zip/3.3.6", "version": "3.3.6"}]}, {"author": "ggets", "description": "Fetch location and size of physical screens - https://github.com/rr-/screeninfo", "issues": "https://github.com/ggets/sublime-screeninfo/issues", "name": "screeninfo", "releases": [{"date": "2020-04-15 14:13:07", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/ggets/sublime-screeninfo/zip/0.6.5", "version": "0.6.5"}]}, {"author": "wbond", "description": "Python select module for Sublime Text 2 on Windows", "issues": "https://github.com/codexns/sublime-select-windows/issues", "name": "select-windows", "releases": [{"date": "2014-12-04 23:50:02", "platforms": ["windows-x32", "windows-x64"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/codexns/sublime-select-windows/zip/1.0.0", "version": "1.0.0"}]}, {"author": "wbond", "description": "Access a user's environmental variables as defined in their shell", "issues": "https://github.com/codexns/shellenv/issues", "name": "shellenv", "releases": [{"date": "2015-10-01 00:40:42", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/codexns/shellenv/zip/1.4.2", "version": "1.4.2"}]}, {"author": "jcoc611", "description": "Python six library https://github.com/benjaminp/six", "issues": "https://github.com/packagecontrol/sublime-six/issues", "name": "six", "releases": [{"date": "2018-06-03 01:52:51", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/sublime-six/zip/v1.11.0", "version": "1.11.0"}]}, {"author": "facelessuser", "description": "A modern CSS selector implementation for Beautiful Soup. - https://github.com/facelessuser/soupsieve", "issues": "https://github.com/facelessuser/soupsieve/issues", "name": "soupsieve", "releases": [{"date": "2025-04-20 18:50:07", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "6e60cc5c1ffaf1cebcc12e8188320b72071e922c2e897f737cadce79ad5d30c4", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/e7/9c/0e6afc12c269578be5c0c1c9f4b49a8d32770a080260c333ac04cc1c832d/soupsieve-2.7-py3-none-any.whl", "version": "2.7"}]}, {"author": "<PERSON>berg", "description": "A PEG-based parser interpreter with memoization - https://github.com/avakar/speg", "issues": "https://github.com/idleberg/sublime-speg/issues", "name": "speg", "releases": [{"date": "2021-04-13 21:25:58", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/idleberg/sublime-speg/zip/0.3.0", "version": "0.3.0"}]}, {"author": "wbond", "description": "Python _sqlite3 module", "issues": "https://github.com/codexns/sublime-sqlite3/issues", "name": "sqlite3", "releases": [{"date": "2015-09-12 12:28:41", "platforms": ["osx-x64", "linux-x32", "linux-x64", "windows-x32", "windows-x64"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/codexns/sublime-sqlite3/zip/1.0.1", "version": "1.0.1"}]}, {"author": "huot25", "description": "Pyton module for Sublime Text to automatically style popups based on active color scheme.", "issues": "https://github.com/huot25/StyledPopup/issues", "name": "StyledPopup", "releases": [{"date": "2015-09-11 04:58:04", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/huot25/StyledPopup/zip/1.0.1", "version": "1.0.1"}]}, {"author": "DeathAxe", "description": "AsyncIO for Sublime Text", "issues": "https://github.com/packagecontrol/sublime_aio/issues", "name": "sublime_aio", "releases": [{"date": "2025-05-14 18:25:15", "platforms": ["*"], "python_versions": ["3.8"], "sublime_text": "*", "url": "https://github.com/packagecontrol/sublime_aio/releases/download/v0.1.5/sublime_aio-0.1.5-py3-none-any.whl", "version": "0.1.5"}]}, {"author": "<PERSON>", "description": "Utility library for frequently used functionality in Sublime Text library and convenience functions or classes", "issues": "https://github.com/SublimeText/sublime_lib/issues", "name": "sublime_lib", "releases": [{"date": "2021-05-06 15:10:29", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/SublimeText/sublime_lib/zip/v1.5.2", "version": "1.5.2"}]}, {"author": "mrelusive", "description": "Python module for the Perforce API, extracted from https://pypi.python.org/pypi/P4Python.", "issues": "https://github.com/claytonlemons/SublimeP4Python/issues", "name": "SublimeP4Python", "releases": [{"date": "2016-12-09 17:46:06", "platforms": ["windows-x32", "windows-x64"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/claytonlemons/SublimeP4Python/zip/1.0.0", "version": "1.0.0"}]}, {"author": "csch0", "description": "Python tabulate module", "issues": "https://github.com/packagecontrol/tabulate/issues", "name": "tabulate", "releases": [{"date": "2015-07-12 19:01:00", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/tabulate/zip/v0.7.5", "version": "0.7.5"}]}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "description": "Python module for interfacing with IBM TM1 Planning Analytics", "issues": "https://github.com/ajmyers/TM1py/issues", "name": "TM1py", "releases": [{"date": "2022-02-10 22:53:37", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/ajmyers/TM1py/zip/v10.2.2", "version": "10.2.2"}]}, {"author": "OpenAI", "description": "tiktoken is a fast BPE tokeniser for use with OpenAI's models.", "issues": "https://github.com/openai/tiktoken/issues", "name": "tiktoken", "releases": [{"date": "2024-05-13 18:03:16", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "131b8aeb043a8f112aad9f46011dced25d62629091e51d9dc1adbf4a1cc6aa98", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/25/26/93a0008eb770141cad7733b7abc1315c64ec86ec60a3578d275eb10d42cf/tiktoken-0.7.0-cp38-cp38-win_amd64.whl", "version": "0.7.0"}, {"date": "2024-05-13 18:03:05", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "2398fecd38c921bcd68418675a6d155fad5f5e14c2e92fcf5fe566fa5485a858", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/37/f3/f2beec797daae3b03d75d4dd8925e9425f3939d8de903e412dc3fa7a9636/tiktoken-0.7.0-cp38-cp38-macosx_10_9_x86_64.whl", "version": "0.7.0"}, {"date": "2024-05-13 18:03:07", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "8f5f6afb52fb8a7ea1c811e435e4188f2bef81b5e0f7a8635cc79b0eef0193d6", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/e6/04/9a2b978beea18ef8916319e51f6d079c804062df80c63f0392202e5ddf10/tiktoken-0.7.0-cp38-cp38-macosx_11_0_arm64.whl", "version": "0.7.0"}, {"date": "2024-05-13 18:03:10", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "54031f95c6939f6b78122c0aa03a93273a96365103793a22e1793ee86da31685", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/77/ba/e4149aa0724a5268ab10014a028668c6ef8834862f955ef0c6758240f6d1/tiktoken-0.7.0-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "version": "0.7.0"}, {"date": "2024-05-13 18:03:09", "platforms": ["linux-arm64"], "python_versions": ["3.8"], "sha256": "861f9ee616766d736be4147abac500732b505bf7013cfaf019b85892637f235e", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/1e/43/39ab47b3915f1172ec3857f3c0d361281e08b6de251fec81ce5cfd762fe1/tiktoken-0.7.0-cp38-cp38-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "version": "0.7.0"}]}, {"author": "<PERSON>", "description": "Python lib for TOML", "issues": "https://github.com/jgirardet/sublime-toml/issues", "name": "toml", "releases": [{"date": "2018-11-09 07:20:52", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/jgirardet/sublime-toml/zip/1.0.0", "version": "1.0.0"}]}, {"author": "<PERSON>", "description": "A functional standard library for Python.", "issues": "https://github.com/pytoolz/toolz/issues", "name": "toolz", "releases": [{"date": "2024-10-04 16:17:01", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "292c8f1c4e7516bf9086f8850935c799a874039c8bcf959d47b600e4c44a6236", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/03/98/eb27cc78ad3af8e302c9d8ff4977f5026676e130d28dd7578132a457170c/toolz-1.0.0-py3-none-any.whl", "version": "1.0.0"}]}, {"author": "FriendFeed", "description": "Tornado is a Python web framework and asynchronous networking library.", "issues": "https://github.com/tornadoweb/tornado/issues", "name": "tornado", "releases": [{"date": "2024-11-22 03:06:36", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "908b71bf3ff37d81073356a5fadcc660eb10c1476ee6e2725588626ce7e5ca38", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/61/cc/58b1adeb1bb46228442081e746fcdbc4540905c87e8add7c277540934edb/tornado-6.4.2-cp38-abi3-win_amd64.whl", "version": "6.4.2"}, {"date": "2024-11-22 03:06:34", "platforms": ["windows-x32"], "python_versions": ["3.8"], "sha256": "2876cef82e6c5978fde1e0d5b1f919d756968d5b4282418f3146b79b58556482", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/b5/25/36dbd49ab6d179bcfc4c6c093a51795a4f3bed380543a8242ac3517a1751/tornado-6.4.2-cp38-abi3-win32.whl", "version": "6.4.2"}, {"date": "2024-11-22 03:06:22", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "072ce12ada169c5b00b7d92a99ba089447ccc993ea2143c9ede887e0937aa803", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/96/44/87543a3b99016d0bf54fdaab30d24bf0af2e848f1d13d34a3a5380aabe16/tornado-6.4.2-cp38-abi3-macosx_10_9_x86_64.whl", "version": "6.4.2"}, {"date": "2024-11-22 03:06:20", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "e828cce1123e9e44ae2a50a9de3055497ab1d0aeb440c5ac23064d9e44880da1", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/26/7e/71f604d8cea1b58f82ba3590290b66da1e72d840aeb37e0d5f7291bd30db/tornado-6.4.2-cp38-abi3-macosx_10_9_universal2.whl", "version": "6.4.2"}, {"date": "2024-11-22 03:06:27", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "bca9eb02196e789c9cb5c3c7c0f04fb447dc2adffd95265b2c7223a8a615ccbf", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/22/55/b78a464de78051a30599ceb6983b01d8f732e6f69bf37b4ed07f642ac0fc/tornado-6.4.2-cp38-abi3-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "version": "6.4.2"}, {"date": "2024-11-22 03:06:25", "platforms": ["linux-x32"], "python_versions": ["3.8"], "sha256": "c36e62ce8f63409301537222faffcef7dfc5284f27eec227389f2ad11b09d946", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/4f/3b/e31aeffffc22b475a64dbeb273026a21b5b566f74dee48742817626c47dc/tornado-6.4.2-cp38-abi3-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", "version": "6.4.2"}, {"date": "2024-11-22 03:06:24", "platforms": ["linux-arm64"], "python_versions": ["3.8"], "sha256": "1a017d239bd1bb0919f72af256a970624241f070496635784d9bf0db640d3fec", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/cb/fb/fdf679b4ce51bcb7210801ef4f11fdac96e9885daa402861751353beea6e/tornado-6.4.2-cp38-abi3-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "version": "6.4.2"}]}, {"author": "tree-sitter", "description": "Python bindings to the Tree-sitter parsing library", "issues": "https://github.com/tree-sitter/py-tree-sitter/issues", "name": "tree-sitter", "releases": [{"date": "2024-03-26 10:53:23", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "50c91353a26946e4dd6779837ecaf8aa123aafa2d3209f261ab5280daf0962f5", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/cf/67/8a83076e07e57f809bf308973aac5c21f8a2e5757ddd3429a14986a76405/tree_sitter-0.21.3-cp38-cp38-win_amd64.whl", "version": "0.21.3"}, {"date": "2024-03-26 10:53:12", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "4986a8cb4acebd168474ec2e5db440e59c7888819b3449a43ce8b17ed0331b07", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/a9/08/3a2faaca576eca4ba9d25ff54f5e3c9fe7bdde7b748208dad7c033106f77/tree_sitter-0.21.3-cp38-cp38-macosx_10_9_x86_64.whl", "version": "0.21.3"}, {"date": "2024-03-26 10:53:14", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "6e217fee2e7be7dbce4496caa3d1c466977d7e81277b677f954d3c90e3272ec2", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/d4/9c/0465da56c8ca82a366d08f62be21df61f86116fd63dd70fb9bb94c67b21c/tree_sitter-0.21.3-cp38-cp38-macosx_11_0_arm64.whl", "version": "0.21.3"}, {"date": "2024-03-26 10:53:18", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "f3652ac9e47cdddf213c5d5d6854194469097e62f7181c0a9aa8435449a163a9", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/ec/19/5e964315acf83dc84ddf7d1abadda3d7950a743247cb331c6dea0664fd9f/tree_sitter-0.21.3-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "version": "0.21.3"}, {"date": "2024-03-26 10:53:16", "platforms": ["linux-arm64"], "python_versions": ["3.8"], "sha256": "f32a88afff4f2bc0f20632b0a2aa35fa9ae7d518f083409eca253518e0950929", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/81/aa/28c6e2ee7506b6627ba5a35dcfa9042043a32d14cecb5bc7d8fa6f0a1441/tree_sitter-0.21.3-cp38-cp38-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "version": "0.21.3"}, {"date": "2023-11-13 06:42:27", "platforms": ["windows-x32"], "python_versions": ["3.8"], "sha256": "6a77ac3cdcddd80cdd1fd394318bff99f94f37e08d235aaefccb87e1224946e5", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/31/2e/4aba9c2ce846354033ddad44fc86ac3dcb58d57eaaf3fa93d9080fad28dd/tree_sitter-0.20.4-cp38-cp38-win32.whl", "version": "0.20.4"}]}, {"author": "<PERSON><PERSON><PERSON>", "description": "Binary Python wheels for all tree sitter languages.", "issues": "https://github.com/grantjenks/py-tree-sitter-languages/issues", "name": "tree-sitter-languages", "releases": [{"date": "2024-02-04 10:29:42", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "46a13f7d38f2eeb75f7cf127d1201346093748c270d686131f0cbc50e42870a1", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/0a/c6/44ecfc00af25136106b64e929fcd5df370f77cf9af03b360d8c4b982c743/tree_sitter_languages-1.10.2-cp38-cp38-win_amd64.whl", "version": "1.10.2"}, {"date": "2024-02-04 10:29:40", "platforms": ["windows-x32"], "python_versions": ["3.8"], "sha256": "92d734fb968fe3927a7596d9f0459f81a8fa7b07e16569476b28e27d0d753348", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/e7/85/96bc04cb2f0cf40a71d87eb1867f150e4cb579160c10f5713e2d917eb1eb/tree_sitter_languages-1.10.2-cp38-cp38-win32.whl", "version": "1.10.2"}, {"date": "2024-02-04 10:29:21", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "6f85d1edaa2d22d80d4ea5b6d12b95cf3644017b6c227d0d42854439e02e8893", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/ab/09/93570ecbd8bba8f7e81d607e2fd0eb47afc300fcf862f6084e7b240c2245/tree_sitter_languages-1.10.2-cp38-cp38-macosx_10_9_x86_64.whl", "version": "1.10.2"}, {"date": "2024-02-04 10:29:23", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "d78feed4a764ef3141cb54bf00fe94d514d8b6e26e09423e23b4c616fcb7938c", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/65/88/41ad37577218be07651aac92ef4ab371c5a4895a4d86ded088b014febdd4/tree_sitter_languages-1.10.2-cp38-cp38-macosx_11_0_arm64.whl", "version": "1.10.2"}, {"date": "2024-02-04 10:29:30", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "99d3249beaef2c9fe558ecc9a97853c260433a849dcc68266d9770d196c2e102", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/34/e8/50cfb3613e00fd3106b2b0cb78558671913b61b2c4ed39b803f0fa6b6093/tree_sitter_languages-1.10.2-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "version": "1.10.2"}, {"date": "2024-02-04 10:29:25", "platforms": ["linux-arm64"], "python_versions": ["3.8"], "sha256": "da1aca27531f9dd5308637d76643372856f0f65d0d28677d1bcf4211e8ed1ad0", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/72/98/bf3cb6edb05d3b02d1854f8a37932071f0842caca2b10c22c0435966a215/tree_sitter_languages-1.10.2-cp38-cp38-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "version": "1.10.2"}]}, {"author": "FichteFoll", "description": "Python typing module", "issues": "https://github.com/packagecontrol/typing/issues", "name": "typing", "releases": [{"date": "2019-07-11 15:03:27", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/typing/zip/v3.7.4", "version": "3.7.4"}]}, {"author": "python", "description": "Backported and Experimental Type Hints for Python 3.8+", "issues": "https://github.com/python/typing_extensions/issues", "name": "typing-extensions", "releases": [{"date": "2025-04-10 14:19:03", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "a439e7c04b49fec3e5d3e2beaa21755cadbbdc391694e28ccdd36ca4a1408f8c", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/8b/54/b1ae86c0973cc6f0210b53d508ca3641fb6d0c56823f288d108bc7ab3cc8/typing_extensions-4.13.2-py3-none-any.whl", "version": "4.13.2"}]}, {"author": "<PERSON><PERSON>", "description": "HTTP library with thread-safe connection pooling, file post, and more.", "issues": "https://github.com/urllib3/urllib3/issues", "name": "urllib3", "releases": [{"date": "2024-09-12 10:52:16", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "ca899ca043dcb1bafa3e262d73aa25c465bfb49e0bd9dd5d59f1d0acba2f8fac", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/ce/d9/5f4c13cecde62396b0d3fe530a50ccea91e7dfc1ccf0e09c228841bb5ba8/urllib3-2.2.3-py3-none-any.whl", "version": "2.2.3"}]}, {"author": "terminalfi", "description": "Vision is a minihtml generation library for Sublime Text that enforces minihtml restrictions for tags, styles and attrivutes", "issues": "https://github.com/terminalfi/vision/issues", "name": "vision", "releases": [{"date": "2024-04-27 02:48:59", "platforms": ["*"], "python_versions": ["3.8"], "sublime_text": "*", "url": "https://codeload.github.com/terminalfi/vision/zip/v0.1.5", "version": "0.1.5"}]}, {"author": "gorakhargosh", "description": "Python library and shell utilities to monitor filesystem events.", "issues": "https://github.com/gorakhargosh/watchdog/issues", "name": "watchdog", "releases": [{"date": "2024-08-11 07:37:57", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "c344453ef3bf875a535b0488e3ad28e341adbd5a9ffb0f7d62cefacc8824ef2b", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/99/2e/b69dfaae7a83ea64ce36538cc103a3065e12c447963797793d5c0a1d5130/watchdog-4.0.2-py3-none-win_amd64.whl", "version": "4.0.2"}, {"date": "2024-08-11 07:37:56", "platforms": ["windows-x32"], "python_versions": ["3.8"], "sha256": "0d8a7e523ef03757a5aa29f591437d64d0d894635f8a50f370fe37f913ce4e19", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/d0/d8/cdb0c21a4a988669d7c210c75c6a2c9a0e16a3b08d9f7e633df0d9a16ad8/watchdog-4.0.2-py3-none-win32.whl", "version": "4.0.2"}, {"date": "2024-08-11 07:37:28", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "f6ee8dedd255087bc7fe82adf046f0b75479b989185fb0bdf9a98b612170eac7", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/6c/3e/064974628cf305831f3f78264800bd03b3358ec181e3e9380a36ff156b93/watchdog-4.0.2-cp38-cp38-macosx_10_9_x86_64.whl", "version": "4.0.2"}, {"date": "2024-08-11 07:37:29", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "0b4359067d30d5b864e09c8597b112fe0a0a59321a0f331498b013fb097406b4", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/23/69/1d2ad9c12d93bc1e445baa40db46bc74757f3ffc3a3be592ba8dbc51b6e5/watchdog-4.0.2-cp38-cp38-macosx_11_0_arm64.whl", "version": "4.0.2"}, {"date": "2024-08-11 07:37:55", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "c0b14488bd336c5b1845cee83d3e631a1f8b4e9c5091ec539406e4a324f882d8", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/01/d2/c8931ff840a7e5bd5dcb93f2bb2a1fd18faf8312e9f7f53ff1cf76ecc8ed/watchdog-4.0.2-py3-none-manylinux2014_x86_64.whl", "version": "4.0.2"}, {"date": "2024-08-11 07:37:45", "platforms": ["linux-arm64"], "python_versions": ["3.8"], "sha256": "936acba76d636f70db8f3c66e76aa6cb5136a936fc2a5088b9ce1c7a3508fc83", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/8a/b1/25acf6767af6f7e44e0086309825bd8c098e301eed5868dc5350642124b9/watchdog-4.0.2-py3-none-manylinux2014_aarch64.whl", "version": "4.0.2"}, {"date": "2016-10-12 13:16:38", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/vovkkk/sublime-watchdog/zip/0.8.31", "version": "0.8.31"}]}, {"author": "facelessuser", "description": "Python wcmatch module which provides enhanced file globbing and matching", "issues": "https://github.com/facelessuser/sublime-wcmatch/issues", "name": "wcmatch", "releases": [{"date": "2024-09-26 18:39:51", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "0dd927072d03c0a6527a20d2e6ad5ba8d0380e60870c383bc533b71744df7b7a", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/ab/df/4ee467ab39cc1de4b852c212c1ed3becfec2e486a51ac1ce0091f85f38d7/wcmatch-10.0-py3-none-any.whl", "version": "10.0"}, {"date": "2021-03-11 21:47:36", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/facelessuser/sublime-wcmatch/zip/1.2.1", "version": "1.2.1"}]}, {"author": "randy3k", "description": "Python wcwidth module", "issues": "https://github.com/packagecontrol/wcwidth/issues", "name": "wcwidth", "releases": [{"date": "2024-01-06 02:10:55", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "3da69048e4540d84af32131829ff948f1e022c1c6bdb8d6102117aac784f6859", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/fd/84/fd2ba7aafacbad3c4201d395674fc6348826569da3c0937e75505ead3528/wcwidth-0.2.13-py2.py3-none-any.whl", "version": "0.2.13"}, {"date": "2018-06-16 17:12:54", "platforms": ["*"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/packagecontrol/wcwidth/zip/v0.1.7", "version": "0.1.7"}]}, {"author": "<PERSON><PERSON><PERSON><PERSON>", "description": "websockets is a library for building WebSocket servers and clients in Python with a focus on correctness, simplicity, robustness, and performance.", "issues": "https://github.com/python-websockets/websockets/issues", "name": "websockets", "releases": [{"date": "2024-09-21 17:33:39", "platforms": ["windows-x64"], "python_versions": ["3.8"], "sha256": "485307243237328c022bc908b90e4457d0daa8b5cf4b3723fd3c4a8012fce4c6", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/8b/b3/945aacb21fc89ad150403cbaa974c9e846f098f16d9f39a3dd6094f9beb1/websockets-13.1-cp38-cp38-win_amd64.whl", "version": "13.1"}, {"date": "2024-09-21 17:33:28", "platforms": ["osx-x64"], "python_versions": ["3.8"], "sha256": "149e622dc48c10ccc3d2760e5f36753db9cacf3ad7bc7bbbfd7d9c819e286f23", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/bb/f7/0610032e0d3981758fdd6ee7c68cc02ebf668a762c5178d3d91748228849/websockets-13.1-cp38-cp38-macosx_10_9_x86_64.whl", "version": "13.1"}, {"date": "2024-09-21 17:33:29", "platforms": ["osx-arm64"], "python_versions": ["3.8"], "sha256": "a569eb1b05d72f9bce2ebd28a1ce2054311b66677fcd46cf36204ad23acead8c", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/55/2f/c43173a72ea395263a427a36d25bce2675f41c809424466a13c61a9a2d61/websockets-13.1-cp38-cp38-macosx_11_0_arm64.whl", "version": "13.1"}, {"date": "2024-09-21 17:33:33", "platforms": ["linux-x64"], "python_versions": ["3.8"], "sha256": "035233b7531fb92a76beefcbf479504db8c72eb3bff41da55aecce3a0f729e54", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/3c/0e/60da63b1c53c47f389f79312b3356cb305600ffad1274d7ec473128d4e6b/websockets-13.1-cp38-cp38-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl", "version": "13.1"}, {"date": "2024-09-21 17:33:30", "platforms": ["linux-arm64"], "python_versions": ["3.8"], "sha256": "95df24ca1e1bd93bbca51d94dd049a984609687cb2fb08a7f2c56ac84e9816ea", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/92/7e/8fa930c6426a56c47910792717787640329e4a0e37cdfda20cf89da67126/websockets-13.1-cp38-cp38-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", "version": "13.1"}]}, {"author": "liris", "description": "WebSocket client for Python with low level API options", "issues": "https://github.com/websocket-client/websocket-client/issues", "name": "websocket-client", "releases": [{"date": "2024-04-23 22:16:14", "platforms": ["*"], "python_versions": ["3.8"], "sha256": "17b44cc997f5c498e809b22cdf2d9c7a9e71c02c8cc2b6c56e7c2d1239bfa526", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/5a/84/44687a29792a70e111c5c477230a72c4b957d88d16141199bf9acb7537a3/websocket_client-1.8.0-py3-none-any.whl", "version": "1.8.0"}, {"date": "2021-05-05 01:43:04", "platforms": ["*"], "python_versions": ["3.3"], "sha256": "2e50d26ca593f70aba7b13a489435ef88b8fc3b5c5643c1ce8808ff9b40f0b32", "sublime_text": "*", "url": "https://files.pythonhosted.org/packages/f7/0c/d52a2a63512a613817846d430d16a8fbe5ea56dd889e89c68facf6b91cb6/websocket_client-0.59.0-py2.py3-none-any.whl", "version": "0.59.0"}]}, {"author": "randy3k", "description": "Automation tool on Linux", "issues": "https://github.com/randy3k/sublime-xdotool/issues", "name": "xdotool", "releases": [{"date": "2016-07-16 04:13:52", "platforms": ["linux"], "python_versions": ["3.3"], "sublime_text": "*", "url": "https://codeload.github.com/randy3k/sublime-xdotool/zip/v3.20160714.1", "version": "3.20160714.1"}]}, {"author": "<PERSON>berg", "description": "Makes working with XML feel like you are working with JSON - https://github.com/martinblech/xmltodict", "issues": "https://github.com/idleberg/sublime-xmltodict/issues", "name": "xmltodict", "releases": [{"date": "2021-04-13 21:32:05", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/idleberg/sublime-xmltodict/zip/0.12.0", "version": "0.12.0"}]}, {"author": "<PERSON>", "description": "Engine for YAML Macros", "issues": "https://github.com/Thom1729/yaml-macros-engine/issues", "name": "yaml_macros_engine", "releases": [{"date": "2024-01-22 18:11:41", "platforms": ["*"], "python_versions": ["3.3", "3.8"], "sublime_text": "*", "url": "https://codeload.github.com/Thom1729/yaml-macros-engine/zip/v2.2.0", "version": "2.2.0"}]}]}, "packages_cache": {"https://raw.githubusercontent.com/packagecontrol/channel/main/repository.json": []}, "repositories": ["https://raw.githubusercontent.com/packagecontrol/channel/main/repository.json"], "schema_version": "4.0.0"}